"""
Basic usage examples for MeOS Python SDK.

This example demonstrates the most common use cases for the MeOS SDK.
"""

import asyncio
from datetime import datetime, timedelta

from meos import AsyncMeOSClient, MeOSClient


def basic_sync_example():
    """Basic synchronous client usage example."""
    # Initialize the client
    client = MeOSClient(base_url="https://your-meos-instance.com", app_id="your_app_id", app_secret="your_app_secret")

    try:
        # Get project information
        project_response = client.project_instances.get_project_info("PROJECT_CODE")
        if project_response.is_success:
            project_info = project_response.data
            print(f"Project Name: {project_info.pro_name}")
            print(f"Project Type: {project_info.pro_type}")
        else:
            print(f"Error: {project_response.msg}")

        # Query dynamic data for the last hour
        end_time = datetime.now()
        start_time = end_time - timedelta(hours=1)

        dynamic_response = client.dynamic_data.get_dynamic_history(
            data_codes=["DATA_CODE_1", "DATA_CODE_2"],
            start_time=start_time.strftime("%Y-%m-%d %H:%M:%S"),
            end_time=end_time.strftime("%Y-%m-%d %H:%M:%S"),
        )

        if dynamic_response.is_success:
            dynamic_data = dynamic_response.data
            print(f"Retrieved {len(dynamic_data)} data points")
            for data_point in dynamic_data[:5]:  # Show first 5 points
                print(f"  {data_point.data_code}: {data_point.value} at {data_point.timestamp}")
        else:
            print(f"Error getting dynamic data: {dynamic_response.msg}")

        # Get real-time data
        realtime_response = client.dynamic_data.get_dynamic_realtime(data_codes=["DATA_CODE_1", "DATA_CODE_2"])

        if realtime_response.is_success:
            realtime_data = realtime_response.data
            print("Real-time data:")
            for data_point in realtime_data:
                print(f"  {data_point.data_code}: {data_point.value}")
        else:
            print(f"Error getting realtime data: {realtime_response.msg}")

    except Exception as e:
        print(f"Error: {e}")


async def basic_async_example():
    """Basic asynchronous client usage example."""
    # Initialize the async client
    async with AsyncMeOSClient(base_url="https://your-meos-instance.com", app_id="your_app_id", app_secret="your_app_secret") as client:
        try:
            # Get project information
            project_response = await client.project_instances.get_project_info_async("PROJECT_CODE")
            if project_response.is_success:
                project_info = project_response.data
                print(f"Project Name: {project_info.pro_name}")

            # Query multiple data sources concurrently
            tasks = [
                client.dynamic_data.get_dynamic_realtime_async(["DATA_CODE_1"]),
                client.dynamic_data.get_dynamic_realtime_async(["DATA_CODE_2"]),
            ]

            results = await asyncio.gather(*tasks)
            realtime_response_1, realtime_response_2 = results

            if realtime_response_1.is_success and realtime_response_1.data:
                print(f"Data 1: {realtime_response_1.data[0].value}")
            else:
                print("Data 1: No data")

            if realtime_response_2.is_success and realtime_response_2.data:
                print(f"Data 2: {realtime_response_2.data[0].value}")
            else:
                print("Data 2: No data")

        except Exception as e:
            print(f"Error: {e}")


def pagination_example():
    """Example of handling paginated results."""
    client = MeOSClient(base_url="https://your-meos-instance.com", app_id="your_app_id", app_secret="your_app_secret")

    try:
        # Get project list
        projects_response = client.project_instances.list_project_instances()

        if projects_response.is_success:
            projects = projects_response.data
            print(f"Found {len(projects)} projects")
            for project in projects:
                print(f"  - {project.pro_name} ({project.data_code})")
        else:
            print(f"Error getting projects: {projects_response.msg}")

    except Exception as e:
        print(f"Error: {e}")


if __name__ == "__main__":
    print("=== Basic Sync Example ===")
    basic_sync_example()

    print("\n=== Basic Async Example ===")
    asyncio.run(basic_async_example())

    print("\n=== Pagination Example ===")
    pagination_example()

"""
Authentication management for MeOS Python SDK.

This module handles token-based authentication with automatic refresh.
"""

import asyncio
import logging
import time
from typing import Dict, Optional

import httpx

from .exceptions import MeOSAuthenticationError, MeOSConnectionError, create_api_error

logger = logging.getLogger(__name__)


class TokenManager:
    """Manages authentication tokens with automatic refresh."""

    def __init__(
        self,
        app_id: str,
        app_secret: str,
        base_url: str,
        token_endpoint: str = "/p/token",
        token_lifetime: int = 7200,  # 2 hours in seconds
        refresh_threshold: int = 300,  # Refresh 5 minutes before expiry
    ) -> None:
        """
        Initialize the token manager.

        Args:
            app_id: Application ID
            app_secret: Application secret
            base_url: Base URL for the MeOS API
            token_endpoint: Token endpoint path
            token_lifetime: Token lifetime in seconds (default: 2 hours)
            refresh_threshold: Refresh token this many seconds before expiry
        """
        self.app_id = app_id
        self.app_secret = app_secret
        self.base_url = base_url.rstrip("/")
        self.token_endpoint = token_endpoint
        self.token_lifetime = token_lifetime
        self.refresh_threshold = refresh_threshold

        self._token: Optional[str] = None
        self._token_expires_at: Optional[float] = None
        self._refresh_lock = asyncio.Lock()

    @property
    def token(self) -> Optional[str]:
        """Get the current token if it's still valid."""
        if self._token and self._is_token_valid():
            return self._token
        return None

    def _is_token_valid(self) -> bool:
        """Check if the current token is still valid."""
        if not self._token or not self._token_expires_at:
            return False

        # Check if token expires within the refresh threshold
        return time.time() < (self._token_expires_at - self.refresh_threshold)

    def _calculate_expiry_time(self) -> float:
        """Calculate when the token will expire."""
        return time.time() + self.token_lifetime

    def authenticate_sync(self, client: httpx.Client) -> str:
        """
        Authenticate synchronously and return a valid token.

        Args:
            client: HTTP client to use for the request

        Returns:
            Valid authentication token

        Raises:
            MeOSAuthenticationError: If authentication fails
        """
        if self.token:
            return self.token

        return self._request_token_sync(client)

    async def authenticate_async(self, client: httpx.AsyncClient) -> str:
        """
        Authenticate asynchronously and return a valid token.

        Args:
            client: Async HTTP client to use for the request

        Returns:
            Valid authentication token

        Raises:
            MeOSAuthenticationError: If authentication fails
        """
        if self.token:
            return self.token

        async with self._refresh_lock:
            # Double-check after acquiring lock
            if self.token:
                return self.token

            return await self._request_token_async(client)

    def _request_token_sync(self, client: httpx.Client) -> str:
        """Request a new token synchronously."""
        try:
            response = client.post(
                self.token_endpoint,
                json={
                    "appId": self.app_id,
                    "appSecret": self.app_secret,
                },
                headers={"Content-Type": "application/json"},
            )

            return self._process_token_response(response)

        except httpx.RequestError as e:
            logger.error(f"Failed to request token: {e}")
            raise MeOSConnectionError(
                f"Failed to connect to authentication endpoint: {e}",
                original_error=e,
            )

    async def _request_token_async(self, client: httpx.AsyncClient) -> str:
        """Request a new token asynchronously."""
        try:
            response = await client.post(
                self.token_endpoint,
                json={
                    "appId": self.app_id,
                    "appSecret": self.app_secret,
                },
                headers={"Content-Type": "application/json"},
            )

            return self._process_token_response(response)

        except httpx.RequestError as e:
            logger.error(f"Failed to request token: {e}")
            raise MeOSConnectionError(
                f"Failed to connect to authentication endpoint: {e}",
                original_error=e,
            )

    def _process_token_response(self, response: httpx.Response) -> str:
        """Process the token response and extract the token."""
        try:
            response_data = response.json()
        except Exception as e:
            logger.error(f"Failed to parse token response: {e}")
            raise MeOSAuthenticationError("Invalid response format from authentication endpoint")

        # Check for HTTP errors
        if response.status_code != 200:
            logger.error(f"Authentication failed with status {response.status_code}")
            raise create_api_error(response.status_code, response_data)

        # Check for API errors
        if response_data.get("code") != 200:
            error_msg = response_data.get("msg", "Authentication failed")
            logger.error(f"Authentication failed: {error_msg}")
            raise MeOSAuthenticationError(
                error_msg,
                code=response_data.get("code"),
                status_code=response.status_code,
                response_data=response_data,
            )

        # Extract token
        data = response_data.get("data", {})
        token = data.get("token")

        if not token:
            logger.error("No token in authentication response")
            raise MeOSAuthenticationError("No token received from authentication endpoint")

        # Store token and expiry time
        self._token = token
        self._token_expires_at = self._calculate_expiry_time()

        logger.info("Successfully authenticated and received token")
        return token

    def invalidate_token(self) -> None:
        """Invalidate the current token, forcing a refresh on next use."""
        self._token = None
        self._token_expires_at = None
        logger.info("Token invalidated")

    def get_auth_headers(self) -> Dict[str, str]:
        """
        Get authentication headers for API requests.

        Returns:
            Dictionary containing authentication headers
        """
        if self.token:
            return {"Siact-token": self.token}
        return {}

    def __repr__(self) -> str:
        return f"TokenManager(app_id={self.app_id!r}, has_token={self._token is not None}, expires_at={self._token_expires_at})"

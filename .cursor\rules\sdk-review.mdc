---
description: 基于 openapi的mcp实现的sdk代码review报告
globs:
alwaysApply: false
---

你是一个专业的代码审查员和 OpenAPI 专家.请帮助我 review 我基于 OpenAPI 接口规范开发的 MeOS™ Python 的 SDK代码.

### 背景说明:
我已经通过 Cursor 结合 MCP 工具, 根据一个 OpenAPI 文档生成了一个关于MeOS™的 Python SDK代码.现在我希望你:

---

## 🎯 审查目标:

### 🔹 ① 功能完整性检查 (Coverage Check)
- 分析 SDK 中是否实现了 OpenAPI 中定义的所有接口.
- 如果有未实现的接口, 请指出其路径、方法以及描述.
- 输出一份"接口覆盖率"统计表 (如: 总接口数、已实现数、缺失数、缺失接口列表) .

### 🔹 ② 实现正确性检查 (Correctness Check)
对于每个已实现的接口, 请逐项验证以下内容是否与 OpenAPI 定义一致:

1. **请求参数 (Request Parameters) **
   - 路径参数 (Path Parameters)
   - 查询参数 (Query Parameters)
   - 请求体参数 (Body Parameters)
   - 是否可选/必填是否一致

2. **响应返回 (Responses) **
   - 正常响应 (例如 `200 OK`) 的结构是否匹配
   - 错误响应 (例如 `400`, `401`, `500` 等) 是否被处理或注释说明
   - 响应数据类型是否与 OpenAPI 定义一致

3. **命名一致性**
   - 函数名、参数名、字段名是否与 OpenAPI 中的命名风格保持一致 (如 snake_case vs camelCase)
   - 对于通过类似alias或者current/size等于page/size这种映射替换的, 全部不允许

4. **类型注解**
   - 是否使用了合适的类型提示 (Type Hints) 以提高可读性和安全性

5. **异常处理**
   - 是否统一处理错误状态码并抛出合适的异常

6. **文档字符串 (docstring) **
   - 是否为每个函数添加了 docstring 并包含接口用途、参数说明、返回值等信息

---

## 📄 输出格式要求:

请输出一份结构化的 **Review Report**, 包括以下部分:

### 1. 🧩 总体概览
- 总接口数
- 已实现接口数
- 缺失接口数
- 接口覆盖率百分比

### 2. 📌 缺失接口清单 (如有)
- 方法 + 路径
- 描述 (来自 OpenAPI)
- 可能影响的功能模块

### 3. 🔍 接口实现细节分析 (可选抽样或全量)
选择几个典型接口, 列出以下对比信息:
| 项目 | OpenAPI 定义 | SDK 实现 | 是否一致 | 备注 |
|------|---------------|------------|----------|------|
| 请求方法 | GET | GET | ✅ | - |
| 参数 `id` | 必填, int | 可选, str | ❌ | 类型不符 |
| 返回类型 `200` | User object | dict | ⚠️ | 类型抽象不一致 |

### 4. 📊 合规性评分 (建议打分方式)
- 参数匹配度: xx%
- 返回值匹配度: xx%
- 文档完整度: xx%

### 5. 🛠️ 改进建议
- 哪些地方需要重构？
- 是否推荐引入 Pydantic 进行数据建模？
- 是否推荐统一异常类？
- 是否推荐增加单元测试？

---

## 📂 提供资料:
- OpenAPI 文件 (JSON/YAML)
- SDK 的源码文件 (目录结构 + 所有相关 `.py` 文件)

---

## 📝 最终交付:
请以 Markdown 格式输出一份完整的 **Review Report**, 便于阅读和后续改进.

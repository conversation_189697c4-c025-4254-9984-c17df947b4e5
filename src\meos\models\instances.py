"""
Instance models for MeOS Python SDK.

This module contains models for various instance types in the MeOS system.
"""

from __future__ import annotations

from typing import List, Optional

from pydantic import Field

from .base import BaseModel, BaseResponse, PageResponse
from .common import DynamicPropertyValue, StaticPropertyValue


class BaseInstanceInfo(BaseModel):
    """Base instance information model."""

    dataCode: Optional[str] = Field(None, description="数据编码")
    descr: Optional[str] = Field(None, description="描述")
    reserve1: Optional[str] = Field(None, description="预留1")
    reserve2: Optional[str] = Field(None, description="预留2")
    reserve3: Optional[str] = Field(None, description="预留3")
    reserve4: Optional[str] = Field(None, description="预留4")
    reserve5: Optional[str] = Field(None, description="预留5")


class ProjectInstance(BaseInstanceInfo):
    """Project instance model."""

    proType: Optional[str] = Field(None, description="项目类别")
    proName: Optional[str] = Field(None, description="项目名称")


class StationInstance(BaseInstanceInfo):
    """Station instance model."""

    stName: Optional[str] = Field(None, description="站点名称")
    stCode: Optional[str] = Field(None, description="站点短码")


class _EqCommonInfoModel(BaseModel):
    usefulLife: Optional[int] = Field(None, description="使用寿命(年)")
    proDate: Optional[str] = Field(None, description="生产日期")


class EquipmentInstance(BaseInstanceInfo, _EqCommonInfoModel):
    """Equipment instance model."""

    eqName: Optional[str] = Field(None, description="设备名称")
    eqCode: Optional[str] = Field(None, description="设备短码")


class UnitInstance(BaseInstanceInfo):
    """Unit instance model."""

    uniName: Optional[str] = Field(None, description="单元名称")
    uniCode: Optional[str] = Field(None, description="单元短码")


class ElementInstance(BaseInstanceInfo, _EqCommonInfoModel):
    """Element instance model."""

    elName: Optional[str] = Field(None, description="元件名称")
    elCode: Optional[str] = Field(None, description="元件类型")


class SystemInstance(BaseInstanceInfo):
    """System instance model."""

    sysName: Optional[str] = Field(None, description="系统名称")
    sysCode: Optional[str] = Field(None, description="系统短码")


class MeterInstance(BaseInstanceInfo):
    """Meter instance model."""

    meterName: Optional[str] = Field(None, description="表计名称")
    meterType: Optional[str] = Field(None, description="表计类型")


class PipeInstance(BaseInstanceInfo):
    """Pipe instance model."""

    pipeCode: Optional[str] = Field(None, description="管路短码")
    pipeName: Optional[str] = Field(None, description="管路名称")


class ProductionEnergySystemInstance(BaseModel):
    """Production energy system instance model."""

    parentInsId: Optional[int] = Field(None, description="父实例id")
    parentInsDataCode: Optional[str] = Field(None, description="父实例DataCode")
    id: Optional[int] = Field(None, description="ID")
    dataCode: Optional[str] = Field(None, description="生产能耗系统编码")
    pcmCode: Optional[str] = Field(None, description="生产能耗系统短码")
    pcmType: Optional[str] = Field(None, description="生产能耗系统类型")
    pcmName: Optional[str] = Field(None, description="生产能耗系统名称")
    descr: Optional[str] = Field(None, description="描述")
    proName: Optional[str] = Field(None, description="项目名称")


class FactoryAreaInstance(BaseModel):
    """厂区实例VO"""

    parentInsId: Optional[int] = Field(None, description="父实例id")
    parentInsDataCode: Optional[str] = Field(None, description="父实例DataCode")
    id: Optional[int] = Field(None, description="ID")
    dataCode: Optional[str] = Field(None, description="数字化编码")
    serial: Optional[int] = Field(None, description="序号")
    pcmFactoryDistrictCode: Optional[str] = Field(None, description="厂区编码")
    pcmFactoryDistrictType: Optional[str] = Field(None, description="厂区类型")
    pcmFactoryDistrictName: Optional[str] = Field(None, description="厂区名称")
    descr: Optional[str] = Field(None, description="描述")


class FactoryAreaInfoResponse(BaseResponse[FactoryAreaInstance]):
    """响应对象«厂区实例VO»"""

    pass


class FactoryBuildingInstance(BaseModel):
    """厂房实例VO"""

    parentInsId: Optional[int] = Field(None, description="父实例id")
    parentInsDataCode: Optional[str] = Field(None, description="父实例DataCode")
    id: Optional[int] = Field(None, description="ID")
    dataCode: Optional[str] = Field(None, description="数字化编码")
    serial: Optional[int] = Field(None, description="序号")
    pcmFactoryBuildingCode: Optional[str] = Field(None, description="厂房编码")
    pcmFactoryBuildingType: Optional[str] = Field(None, description="厂房类型")
    pcmFactoryBuildingName: Optional[str] = Field(None, description="厂房名称")
    descr: Optional[str] = Field(None, description="描述")


class FactoryBuildingInfoResponse(BaseResponse[FactoryBuildingInstance]):
    """响应对象«厂房实例VO»"""

    pass


class WorkshopInstance(BaseModel):
    """车间实例VO"""

    parentInsId: Optional[int] = Field(None, description="父实例id")
    parentInsDataCode: Optional[str] = Field(None, description="父实例DataCode")
    id: Optional[int] = Field(None, description="ID")
    dataCode: Optional[str] = Field(None, description="数字化编码")
    serial: Optional[int] = Field(None, description="序号")
    pcmMachineShopCode: Optional[str] = Field(None, description="车间编码")
    pcmMachineShopType: Optional[str] = Field(None, description="车间类型")
    pcmMachineShopName: Optional[str] = Field(None, description="车间名称")
    descr: Optional[str] = Field(None, description="描述")


class WorkshopInfoResponse(BaseResponse[WorkshopInstance]):
    """响应对象«车间实例VO»"""

    pass


class ProductionLineInstance(BaseModel):
    """产线实例VO"""

    parentInsId: Optional[int] = Field(None, description="父实例id")
    parentInsDataCode: Optional[str] = Field(None, description="父实例DataCode")
    id: Optional[int] = Field(None, description="ID")
    dataCode: Optional[str] = Field(None, description="数字化编码")
    serial: Optional[int] = Field(None, description="序号")
    pcmProductionLineCode: Optional[str] = Field(None, description="产线编码")
    pcmProductionLineType: Optional[str] = Field(None, description="产线类型")
    pcmProductionLineName: Optional[str] = Field(None, description="产线名称")
    descr: Optional[str] = Field(None, description="描述")


class ProductionLineInfoResponse(BaseResponse[ProductionLineInstance]):
    """响应对象«产线实例VO»"""

    pass


class ProcessInstance(BaseModel):
    """工序实例VO"""

    parentDataCode: Optional[str] = Field(None)
    parentInsId: Optional[int] = Field(None, description="父实例id")
    parentInsDataCode: Optional[str] = Field(None, description="父实例DataCode")
    id: Optional[int] = Field(None, description="ID")
    dataCode: Optional[str] = Field(None, description="数字化编码")
    serial: Optional[int] = Field(None, description="序号")
    pcmProcessNodeCode: Optional[str] = Field(None, description="工序编码")
    pcmProcessNodeType: Optional[str] = Field(None, description="工序类型")
    pcmProcessNodeName: Optional[str] = Field(None, description="工序名称")
    descr: Optional[str] = Field(None, description="描述")


class ProcessInfoResponse(BaseResponse[ProcessInstance]):
    """响应对象«工序实例VO»"""

    pass


class ProductionEquipmentInstance(BaseModel):
    """设备实例VO"""

    parentInsId: Optional[int] = Field(None, description="父实例id")
    parentInsDataCode: Optional[str] = Field(None, description="父实例DataCode")
    id: Optional[int] = Field(None, description="ID")
    parentDataCode: Optional[str] = Field(None, description="上级数字化编码")
    dataCode: Optional[str] = Field(None, description="数字化编码")
    serial: Optional[int] = Field(None, description="序号")
    pcmEqNodeCode: Optional[str] = Field(None, description="设备编码")
    pcmEqNodeType: Optional[str] = Field(None, description="设备类型")
    pcmEqNodeName: Optional[str] = Field(None, description="设备名称")
    descr: Optional[str] = Field(None, description="描述")


class ProductionEquipmentInfoResponse(BaseResponse[ProductionEquipmentInstance]):
    """响应对象«设备实例VO»"""

    pass


# Response models
class ProjectInstanceResponse(BaseResponse[ProjectInstance]):
    """Project instance response model."""

    pass


class StationInstanceResponse(BaseResponse[StationInstance]):
    """Station instance response model."""

    pass


class EquipmentInstanceResponse(BaseResponse[EquipmentInstance]):
    """Equipment instance response model."""

    pass


class UnitInstanceResponse(BaseResponse[UnitInstance]):
    """Unit instance response model."""

    pass


class ElementInstanceResponse(BaseResponse[ElementInstance]):
    """Element instance response model."""

    pass


class SystemInstanceResponse(BaseResponse[SystemInstance]):
    """System instance response model."""

    pass


class MeterInstanceResponse(BaseResponse[MeterInstance]):
    """Meter instance response model."""

    pass


class PipeInstanceResponse(BaseResponse[PipeInstance]):
    """Pipe instance response model."""

    pass


class ProductionEnergyResponse(BaseResponse[ProductionEnergySystemInstance]):
    """Production engergy response model"""

    pass


# List response models
class ProjectInstanceTreeNode(BaseModel):
    insName: Optional[str] = Field(None, description="实例名称")
    dataCode: Optional[str] = Field(None, description="实例编码")
    nodeType: Optional[str] = Field(None, description="节点类型")
    children: Optional[List[ProjectInstanceTreeNode]] = Field(None, description="子数据列表")


class ProjectModelTreeNode(BaseModel):
    """实例树VO-v1"""

    modelName: Optional[str] = Field(None, description="模型名称")
    dataCode: Optional[str] = Field(None, description="模型编码")
    modelCode: Optional[str] = Field(None, description="模型短码")
    nodeType: Optional[str] = Field(
        None, description="节点类型(项目: project; 系统: system; 站点: station; 单元: unit; 管路: pipe; 设备: eq)"
    )
    children: Optional[List[ProjectModelTreeNode]] = Field(None, description="子数据列表")


class InstanceTreeResponse(BaseResponse[List[ProjectModelTreeNode]]):
    """响应对象«List«实例树VO-v1»»"""

    pass


# Property response models
class StaticPropertyListResponse(BaseResponse[PageResponse[StaticPropertyValue]]):
    """Static property list response model."""

    pass


class DynamicPropertyListResponse(BaseResponse[PageResponse[DynamicPropertyValue]]):
    """Dynamic property list response model."""

    pass


# --- project/ins/tree ---
class ProjectInstanceTreeResponse(BaseResponse[List[ProjectInstanceTreeNode]]):
    """响应对象«List«实例树VO»»"""

    pass


class ProjectInstanceListResponse(BaseResponse[List[ProjectInstanceTreeNode]]):
    """响应对象«List«实例列表VO»»"""

    pass


class ProductionEnergyTreeResponse(BaseResponse[List[ProjectInstanceTreeNode]]):
    """生产能耗信息树"""


class PropValSelectModel(BaseModel):
    dictType: Optional[str] = Field(None, description="字典表类型")
    label: Optional[str] = Field(None, description="显示名称")
    remark: Optional[str] = Field(None, description="备注")
    value: Optional[str] = Field(None, description="数值")


# --- project/page/base ---
class ProjectBaseInfoInstance(BaseModel):
    reserve1: Optional[str] = Field(None, description="采集类型大类 (0: 模拟量, 1: 开关量)")
    reserve2: Optional[str] = Field(None, description="采集类型 (0: 累积量, 1: 瞬时量, 2: 开关量)")
    reserve3: Optional[str] = Field(None, description="预留3")
    reserve4: Optional[str] = Field(None, description="预留4")
    reserve5: Optional[str] = Field(None, description="预留5")
    id: Optional[int] = Field(None, description="ID")
    propVal: Optional[str] = Field(None, description="属性取值")
    modelDataCode: Optional[str] = Field(None, description="模型DataCode")
    modelId: Optional[int] = Field(None, description="模型id")
    updateTime: Optional[str] = Field(None, description="属性值更新时间")
    insDataCode: Optional[str] = Field(None, description="实例DataCode")
    insId: Optional[int] = Field(None, description="实例id")
    propValLabel: Optional[str] = Field(None, description="属性取值")
    dataCode: Optional[str] = Field(None, description="属性数字化编码")
    propValSelect: Optional[List[PropValSelectModel]] = Field(None, description="二值和菜单 可选值")
    propCode: Optional[str] = Field(None, description="属性编码")
    serial: Optional[int] = Field(None, description="序号")
    propName: Optional[str] = Field(None, description="属性名称")
    dataType: Optional[str] = Field(None, description="数据类型")
    tfGroup: Optional[str] = Field(None, description="二值分类")
    tfType: Optional[str] = Field(None, description="二值类型")
    menuGroup: Optional[str] = Field(None, description="菜单分类")
    menuType: Optional[str] = Field(None, description="菜单类型")
    singleFlag: Optional[int] = Field(None, description="单选标记 (1单选 0多选) ")
    numType: Optional[str] = Field(None, description="数值类型")
    otherType: Optional[str] = Field(None, description="其它类型")
    p: Optional[int] = Field(None, description="数据精度")
    propType: Optional[str] = Field(None, description="属性类型")
    ratio: Optional[float] = Field(None, description="转换比率")
    maxLen: Optional[int] = Field(None, description="字符长度")
    structDef: Optional[str] = Field(None, description="结构化数据定义")
    formula: Optional[str] = Field(None, description="公式")
    unit: Optional[str] = Field(None, description="单位")
    consType: Optional[str] = Field(None, description="约束类型")
    maxVal: Optional[float] = Field(None, description="最大值")
    minVal: Optional[float] = Field(None, description="最小值")
    valArr: Optional[str] = Field(None, description="取值列表")
    mo: Optional[str] = Field(None, description="必选/可选")
    rw: Optional[str] = Field(None, description="读/写/读写 (仅动态属性支持) ")
    allName: Optional[str] = Field(None, description="英文全称")
    zhDesc: Optional[str] = Field(None, description="中文描述")


class CommonBaseInfoListResponse(BaseResponse[PageResponse[ProjectBaseInfoInstance]]):
    pass


class CommonDynamicPropertyListResponse(BaseResponse[PageResponse[ProjectBaseInfoInstance]]):
    pass


class CommonStaticPropertyListResponse(BaseResponse[PageResponse[ProjectBaseInfoInstance]]):
    pass


class _BaseInfoListRequest(BaseModel):
    dataCode: Optional[str] = Field(None, description="项目编码")
    propTypes: Optional[List[str]] = Field(None, description="属性类型")
    propCode: Optional[str] = Field(None, description="属性短码")
    propName: Optional[str] = Field(None, description="属性名称")
    pageNumber: Optional[int] = Field(None, description="页码")
    pageSize: Optional[int] = Field(None, description="每页条数")


class CommonBaseInfoListRequest(_BaseInfoListRequest):
    pass


class CommonDynamicInfoListRequest(_BaseInfoListRequest):
    pass


class CommonStaticInfoListRequest(_BaseInfoListRequest):
    pass


class MeterInstanceTreeNode(BaseModel):
    selected: Optional[bool] = Field(None, description="是否选中")
    insId: Optional[int] = Field(None, description="实例ID")
    insName: Optional[str] = Field(None, description="实例名称")
    insCode: Optional[str] = Field(None, description="实例编码")
    nodeType: Optional[str] = Field(
        None,
        description="节点类型(项目: project; 系统: system; 站点: station; 单元: unit; 管路: pipe; 设备: eq; 表计: meter)/(厂区: factory_district; 厂房: factory_building; 车间: machine_shop; 产线: production_line; 工序: process_node)",
    )
    dataCode: Optional[str] = Field(None, description="数字化编码")
    parentDataCode: Optional[str] = Field(None, description="父节点数字化编码")
    parentInsName: Optional[str] = Field(None, description="父节点名称")
    children: Optional[List[MeterInstanceTreeNode]] = Field(None, description="子数据列表")
    modelName: Optional[str] = Field(None, description="模型名称")
    modelDataCode: Optional[str] = Field(None, description="模型数字化编码")
    descr: Optional[str] = Field(None, description="备注")
    createTime: Optional[str] = Field(None, description="创建时间")
    modelCode: Optional[str] = Field(None, description="模型编码")


class MeterInstanceTreeResponse(BaseResponse[List[MeterInstanceTreeNode]]):
    """表计实例树响应对象(OpenAPI: data: List[MeterInstanceTreeNode])"""

    pass


class OrderItem(BaseModel):
    column: Optional[str] = Field(None, description="排序字段名")
    asc: Optional[bool] = Field(None, description="是否升序")


class ProductionEnergyPage(PageResponse[ProductionEnergySystemInstance]):
    orders: Optional[List[OrderItem]] = Field(None, description="排序字段信息")


class ProductionEnergyPageResponse(BaseResponse[ProductionEnergyPage]):
    """生产能耗系统分页响应模型(含排序信息)"""

    pass


class TopologyTnodeVO(BaseModel):
    """端点vo"""

    energyType: Optional[str] = Field(None, description="能源类型")
    needCon: Optional[int] = Field(None, description="连接判定")
    nodeType: Optional[str] = Field(None, description="节点类型")
    tnodeCode: Optional[str] = Field(None, description="端点编码")
    tnodeId: Optional[int] = Field(None, description="端点id")
    tnodeIo: Optional[str] = Field(None, description="端点类型")
    tnodeName: Optional[str] = Field(None, description="端点名称")


class TopologyNodeVO(BaseModel):
    """拓扑关系实例VO"""

    insName: Optional[str] = Field(None, description="实例名称")
    insCode: Optional[str] = Field(None, description="实例编码")
    nodeType: Optional[str] = Field(None, description="节点类型(管路: pipe; 设备: eq)")
    tnodes: Optional[List[TopologyTnodeVO]] = Field(None, description="端点vo")


class TopologyLinkVO(BaseModel):
    """连接关系VO"""

    sourceTnodeId: Optional[int] = Field(None, description="来源端点id")
    targetTnodeId: Optional[int] = Field(None, description="目标端点id")


class TopologyVO(BaseModel):
    """设备拓扑关系VO"""

    nodes: Optional[List[TopologyNodeVO]] = Field(None, description="节点")
    links: Optional[List[TopologyLinkVO]] = Field(None, description="连接关系")


class GetTopologyResponse(BaseResponse[TopologyVO]):
    """拓扑关系响应模型"""

    pass

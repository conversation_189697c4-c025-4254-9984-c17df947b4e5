---
globs: src/meos/services/*.py
description: 规范 service 层 endpoint 提取为类属性，避免同步/异步重复写 endpoint
---

# Service Endpoint 统一提取规范

1. 所有 endpoint 信息（如 path、method 等）必须在 service 类的类属性中统一定义，不允许在同步/异步方法体内重复写 endpoint。
2. 浏览文件的整体代码, 分析endpoint和method的必要性
3. endpoint 属性命名建议采用大写+下划线风格（如 `GET_RULE_LIST_ENDPOINT`）
   1. 对于不需要method方法的, 以字符串形式包含 path 字段
   2. 对于需要method方法的, 以 dict 形式包含 path、method 等必要字段。
4. 同步和异步方法均应通过类属性引用 endpoint 信息，如 `self.GET_RULE_LIST_ENDPOINT` 或 `ClassName.GET_RULE_LIST_ENDPOINT`。
5. 禁止在方法体内硬编码 endpoint 信息，必须引用类属性。
6. 该规范适用于 `src/meos/services/` 目录下所有 service 文件。

示例：

```python
class RuleService:
    GET_RULE_LIST_ENDPOINT = {
        "path": "/api/rule/list",
        "method": "GET",
    }

    GET_RULE_LIST1_ENDPOINT = "/api/rule/list"

    def get_rule_list(self, ...):
        endpoint = self.GET_RULE_LIST_ENDPOINT
        endpoint1 = self.GET_RULE_LIST1_ENDPOINT
        # 使用 endpoint['path'], endpoint['method'] 等
```

## 使用方式

1. 迭代services目录下的每一个service文件, 对代码进行审核, 按照规范进行修改代码, 提升代码的整体可维护性

## 约束行为
1. 修改过程中, 切记不要动到其它原有代码, 尤其是接口的请求/响应定义等实现代码, 因为该规范并不涉及对他们的修改
2. 对于只需要path而不需要method的文件, 使用 `GET_RULE_LIST1_ENDPOINT = "/api/rule/list"` 形式即可; 否则使用dict形式来定以endpoint和method

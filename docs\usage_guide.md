# MeOS Python SDK Usage Guide

This guide provides comprehensive examples and best practices for using the MeOS Python SDK.

## Table of Contents

1. [Installation](#installation)
2. [Quick Start](#quick-start)
3. [Authentication](#authentication)
4. [Client Configuration](#client-configuration)
5. [API Services](#api-services)
6. [Error Handling](#error-handling)
7. [Async Usage](#async-usage)
8. [Best Practices](#best-practices)

## Installation

Install the SDK using pip:

```bash
pip install meos-python-sdk
```

For development with optional dependencies:

```bash
pip install meos-python-sdk[dev,test,docs]
```

## Quick Start

### Basic Synchronous Usage

```python
from meos import MeOSClient

# Initialize the client
client = MeOSClient(
    base_url="https://your-meos-instance.com",
    app_id="your_app_id",
    app_secret="your_app_secret"
)

# Get project information
response = client.project_instances.get_project_info("PROJECT_CODE")
if response.is_success:
    project = response.data
    print(f"Project: {project.pro_name}")
else:
    print(f"Error: {response.msg}")

# Clean up resources
client.close()
```

### Using Context Manager

```python
from meos import MeOSClient

with MeOSClient(
    base_url="https://your-meos-instance.com",
    app_id="your_app_id",
    app_secret="your_app_secret"
) as client:
    response = client.dynamic_data.get_dynamic_realtime(
        data_codes=["TEMP_001", "PRESSURE_001"]
    )

    if response.is_success:
        for data_point in response.data:
            print(f"{data_point.data_code}: {data_point.value}")
```

## Authentication

The SDK handles authentication automatically, but you can also manage it manually:

```python
from meos import MeOSClient

client = MeOSClient(
    base_url="https://your-meos-instance.com",
    app_id="your_app_id",
    app_secret="your_app_secret"
)

# Check authentication status
if client.auth.is_authenticated():
    print("Already authenticated")
else:
    print("Not authenticated")

# Get current authentication info
auth_info = client.auth.get_current_auth_info()
print(f"App ID: {auth_info.app_id}")
print(f"Has valid token: {auth_info.has_valid_token}")

# Manually refresh token
try:
    token_response = client.auth.refresh_token()
    print(f"New token obtained: {token_response.data.token}")
except Exception as e:
    print(f"Token refresh failed: {e}")

# Invalidate current token
client.auth.invalidate_token()
```

## Client Configuration

### Basic Configuration

```python
from meos import MeOSClient

client = MeOSClient(
    base_url="https://your-meos-instance.com",
    app_id="your_app_id",
    app_secret="your_app_secret",
    # Optional parameters
    verify_ssl=True,
    timeout={"connect": 10.0, "read": 30.0},
    max_connections=100,
    extra_headers={"User-Agent": "MyApp/1.0"}
)
```

### Advanced Configuration

```python
from meos import ClientConfig, TimeoutConfig, RetryConfig, LoggingConfig

config = ClientConfig(
    base_url="https://your-meos-instance.com",
    app_id="your_app_id",
    app_secret="your_app_secret",

    # Timeout configuration
    timeout=TimeoutConfig(
        connect=10.0,
        read=30.0,
        write=10.0,
        pool=10.0
    ),

    # Retry configuration
    retry=RetryConfig(
        max_retries=3,
        backoff_factor=0.5,
        backoff_max=60.0,
        retry_on_status=(429, 500, 502, 503, 504),
        retry_on_connection_error=True
    ),

    # Logging configuration
    logging=LoggingConfig(
        level="INFO",
        log_requests=True,
        log_responses=True,
        log_request_bodies=False,
        log_response_bodies=False
    ),

    # HTTP configuration
    verify_ssl=True,
    follow_redirects=True,
    max_connections=100,
    max_keepalive_connections=20,
    keepalive_expiry=5.0,

    # Authentication configuration
    token_lifetime=7200,  # 2 hours
    token_refresh_threshold=300,  # 5 minutes before expiry

    # Custom headers
    extra_headers={"User-Agent": "MyApp/1.0"}
)

# Create client from configuration
from meos import create_client_from_config

client = create_client_from_config(config)
```

## API Services

### Dynamic Data Service

```python
from datetime import datetime, timedelta

# Get historical data
end_time = datetime.now()
start_time = end_time - timedelta(hours=1)

history_response = client.dynamic_data.get_dynamic_history(
    data_codes=["TEMP_001", "PRESSURE_001"],
    start_time=start_time.strftime("%Y-%m-%d %H:%M:%S"),
    end_time=end_time.strftime("%Y-%m-%d %H:%M:%S")
)

if history_response.is_success:
    for data_point in history_response.data:
        print(f"{data_point.data_code}: {data_point.value} at {data_point.timestamp}")

# Get real-time data
realtime_response = client.dynamic_data.get_dynamic_realtime(
    data_codes=["TEMP_001", "PRESSURE_001"]
)

if realtime_response.is_success:
    for data_point in realtime_response.data:
        print(f"{data_point.data_code}: {data_point.value}")

# Get real-time data with formula calculation
formula_response = client.dynamic_data.get_dynamic_realtime_with_formula(
    data_codes=["CALC_001"],
    include_formula=True
)

if formula_response.is_success:
    for data_point in formula_response.data:
        print(f"{data_point.data_code}: {data_point.value}")
        if data_point.has_formula:
            print(f"  Formula result: {data_point.formula_result}")
```

### Project Instance Service

```python
from meos.models.common import PaginationRequest

# Get project information
project_response = client.project_instances.get_project_info("PROJECT_001")
if project_response.is_success:
    project = project_response.data
    print(f"Project: {project.pro_name} ({project.pro_type})")

# List all projects
projects_response = client.project_instances.list_project_instances()
if projects_response.is_success:
    for project in projects_response.data:
        print(f"- {project.pro_name}")

# Get project tree
tree_response = client.project_instances.get_project_tree("PROJECT_001")
if tree_response.is_success:
    for node in tree_response.data:
        print(f"Node: {node.instance_name} (Type: {node.instance_type})")

# Get project properties with pagination
pagination = PaginationRequest(page=1, size=20)
properties_response = client.project_instances.get_dynamic_properties(
    pagination=pagination,
    data_code="PROJECT_001"
)

if properties_response.is_success:
    page_data = properties_response.data
    print(f"Page {page_data.current} of {page_data.pages}")
    for prop in page_data.records:
        print(f"- {prop.property_code}: {prop.value}")
```

### Production Energy Service

```python
# Get PCM information
pcm_response = client.production_energy.get_pcm_info("PCM_001")
if pcm_response.is_success:
    pcm_info = pcm_response.data
    print(f"PCM: {pcm_info}")

# Get PCM tree
tree_response = client.production_energy.get_pcm_tree("PCM_001")
if tree_response.is_success:
    for node in tree_response.data:
        print(f"PCM Node: {node}")
```

### Control Service

```python
# Issue control command
command_data = {
    "device_code": "DEVICE_001",
    "command": "START",
    "parameters": {"speed": 100}
}

control_response = client.control.issue_control_command(command_data)
if control_response.is_success:
    print("Control command issued successfully")
else:
    print(f"Control command failed: {control_response.msg}")

# Query feedback value
query_data = {
    "data_codes": ["DEVICE_001_STATUS", "DEVICE_001_SPEED"]
}

feedback_response = client.control.query_feedback_value(query_data)
if feedback_response.is_success:
    for feedback in feedback_response.data:
        print(f"Feedback: {feedback}")
```

## Error Handling

### Exception Types

```python
from meos.exceptions import (
    MeOSError,
    MeOSAPIError,
    MeOSAuthenticationError,
    MeOSConnectionError,
    MeOSTimeoutError
)

try:
    response = client.project_instances.get_project_info("INVALID_CODE")
    if not response.is_success:
        print(f"API Error: {response.code} - {response.msg}")

except MeOSAuthenticationError as e:
    print(f"Authentication failed: {e}")
    # Handle re-authentication

except MeOSConnectionError as e:
    print(f"Connection failed: {e}")
    # Handle connection retry

except MeOSTimeoutError as e:
    print(f"Request timed out: {e}")
    # Handle timeout

except MeOSAPIError as e:
    print(f"API error: {e.status_code} - {e.message}")
    # Handle API-specific errors

except MeOSError as e:
    print(f"General MeOS error: {e}")
    # Handle general errors
```

### Response Validation

```python
def handle_response(response):
    """Helper function to handle API responses."""
    if response.is_success:
        return response.data
    else:
        # Log error details
        print(f"Error {response.code}: {response.msg}")

        # Handle specific error codes
        if response.code == 401:
            print("Authentication required")
        elif response.code == 404:
            print("Resource not found")
        elif response.code >= 500:
            print("Server error - please try again later")

        return None

# Usage
project_response = client.project_instances.get_project_info("PROJECT_001")
project = handle_response(project_response)
if project:
    print(f"Project: {project.pro_name}")
```

## Async Usage

### Basic Async Client

```python
import asyncio
from meos import AsyncMeOSClient

async def main():
    async with AsyncMeOSClient(
        base_url="https://your-meos-instance.com",
        app_id="your_app_id",
        app_secret="your_app_secret"
    ) as client:
        # Get project information
        response = await client.project_instances.get_project_info_async("PROJECT_001")
        if response.is_success:
            project = response.data
            print(f"Project: {project.pro_name}")

# Run async function
asyncio.run(main())
```

### Concurrent Requests

```python
import asyncio
from meos import AsyncMeOSClient

async def fetch_multiple_data():
    async with AsyncMeOSClient(
        base_url="https://your-meos-instance.com",
        app_id="your_app_id",
        app_secret="your_app_secret"
    ) as client:
        # Execute multiple requests concurrently
        tasks = [
            client.project_instances.get_project_info_async("PROJECT_001"),
            client.dynamic_data.get_dynamic_realtime_async(["TEMP_001"]),
            client.production_energy.get_pcm_info_async("PCM_001"),
        ]

        results = await asyncio.gather(*tasks, return_exceptions=True)

        for i, result in enumerate(results):
            if isinstance(result, Exception):
                print(f"Task {i} failed: {result}")
            elif result.is_success:
                print(f"Task {i} succeeded: {type(result.data)}")
            else:
                print(f"Task {i} API error: {result.msg}")

asyncio.run(fetch_multiple_data())
```

### Async Context Management

```python
import asyncio
from meos import AsyncMeOSClient

class DataCollector:
    def __init__(self, base_url: str, app_id: str, app_secret: str):
        self.client = AsyncMeOSClient(base_url, app_id, app_secret)

    async def __aenter__(self):
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.client.aclose()

    async def collect_data(self, data_codes: list[str]):
        response = await self.client.dynamic_data.get_dynamic_realtime_async(data_codes)
        return response.data if response.is_success else []

async def main():
    async with DataCollector(
        "https://your-meos-instance.com",
        "your_app_id",
        "your_app_secret"
    ) as collector:
        data = await collector.collect_data(["TEMP_001", "PRESSURE_001"])
        for point in data:
            print(f"{point.data_code}: {point.value}")

asyncio.run(main())
```

## Best Practices

### 1. Resource Management

Always use context managers to ensure proper resource cleanup:

```python
# Good
with MeOSClient(...) as client:
    response = client.project_instances.get_project_info("PROJECT_001")

# Also good for async
async with AsyncMeOSClient(...) as client:
    response = await client.project_instances.get_project_info_async("PROJECT_001")
```

### 2. Error Handling

Always check response success before accessing data:

```python
# Good
response = client.dynamic_data.get_dynamic_realtime(["TEMP_001"])
if response.is_success:
    data = response.data
    # Process data
else:
    print(f"Error: {response.msg}")

# Bad - may raise AttributeError if data is None
data = client.dynamic_data.get_dynamic_realtime(["TEMP_001"]).data
```

### 3. Configuration

Use configuration objects for complex setups:

```python
from meos import ClientConfig, create_client_from_config

config = ClientConfig(
    base_url="https://your-meos-instance.com",
    app_id="your_app_id",
    app_secret="your_app_secret",
    timeout={"connect": 10.0, "read": 30.0},
    retry={"max_retries": 3, "backoff_factor": 0.5}
)

client = create_client_from_config(config)
```

### 4. Logging

Enable logging for debugging:

```python
import logging
from meos import MeOSClient

# Configure logging
logging.basicConfig(level=logging.DEBUG)

client = MeOSClient(
    base_url="https://your-meos-instance.com",
    app_id="your_app_id",
    app_secret="your_app_secret",
    logging={"level": "DEBUG", "log_requests": True}
)
```

### 5. Pagination

Handle paginated responses properly:

```python
from meos.models.common import PaginationRequest

def get_all_properties(client, data_code):
    """Get all properties using pagination."""
    all_properties = []
    page = 1

    while True:
        pagination = PaginationRequest(page=page, size=100)
        response = client.project_instances.get_dynamic_properties(
            pagination=pagination,
            data_code=data_code
        )

        if not response.is_success:
            break

        page_data = response.data
        all_properties.extend(page_data.records)

        if not page_data.has_next:
            break

        page += 1

    return all_properties
```

### 6. Async Best Practices

Use asyncio.gather for concurrent requests:

```python
async def fetch_multiple_projects(client, project_codes):
    """Fetch multiple projects concurrently."""
    tasks = [
        client.project_instances.get_project_info_async(code)
        for code in project_codes
    ]

    responses = await asyncio.gather(*tasks, return_exceptions=True)

    projects = []
    for response in responses:
        if isinstance(response, Exception):
            print(f"Error: {response}")
        elif response.is_success:
            projects.append(response.data)

    return projects
```

### 7. Token Management

Let the SDK handle token management automatically:

```python
# Good - automatic token management
client = MeOSClient(
    base_url="https://your-meos-instance.com",
    app_id="your_app_id",
    app_secret="your_app_secret",
    token_lifetime=7200,  # 2 hours
    token_refresh_threshold=300  # Refresh 5 minutes before expiry
)

# The SDK will automatically refresh tokens as needed
response = client.project_instances.get_project_info("PROJECT_001")
```

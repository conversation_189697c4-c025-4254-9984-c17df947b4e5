# Changelog

All notable changes to the MeOS Python SDK will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [Unreleased]

### Added
- Initial release of MeOS Python SDK
- Complete API coverage for all MeOS endpoints organized by functional domains
- Both synchronous and asynchronous client implementations
- Comprehensive type safety with Pydantic models
- Automatic token management and refresh
- Built-in retry mechanisms with exponential backoff
- Structured logging for debugging and monitoring
- Complete API response format preservation (`{code, msg, data}`)
- Configuration management with validation
- Comprehensive exception hierarchy
- Context manager support for resource cleanup
- Full test suite with unit and integration tests
- Documentation and usage examples

### Services Implemented
- **Authentication Service** - Token management and authentication
- **Dynamic Data Service** - Dynamic property data operations (动态属性数据)
- **Project Instance Service** - Project instance information (项目实例信息)
- **Station Instance Service** - Station instance information (站点实例信息)
- **Equipment Instance Service** - Equipment instance information (设备实例信息)
- **Unit Instance Service** - Unit instance information (单元实例信息)
- **Element Instance Service** - Element instance information (元件实例信息)
- **System Instance Service** - System instance information (系统实例信息)
- **Meter Instance Service** - Meter instance information (表计实例信息)
- **Pipe Instance Service** - Pipe instance information (管路实例信息)
- **Production Energy Service** - Production energy information (生产能耗信息)
- **Topology Service** - Topology relationships (拓扑关系)
- **Factory Area Service** - Factory area information (厂区信息)
- **Factory Building Service** - Factory building information (厂房信息)
- **Workshop Service** - Workshop information (车间信息)
- **Production Line Service** - Production line information (产线信息)
- **Process Service** - Process information (工序信息)
- **Production Equipment Service** - Production equipment information (设备信息)
- **Control Service** - Control operations (控制接口)
- **Calculation Service** - Calculation framework (计算框架-数据调用)

### Features
- **Type Safety**: Complete type hints and Pydantic models for all data structures
- **Async/Sync Support**: Both synchronous and asynchronous client implementations
- **Authentication**: Automatic token management with configurable refresh thresholds
- **Error Handling**: Comprehensive exception hierarchy with detailed error information
- **Retry Logic**: Built-in retry mechanisms with exponential backoff and configurable parameters
- **Logging**: Structured logging with configurable levels and request/response logging
- **Configuration**: Flexible configuration system with validation
- **Testing**: Comprehensive test suite with pytest and async support
- **Documentation**: Complete API documentation and usage examples

### Technical Details
- **Python Version**: Requires Python 3.10+
- **Dependencies**: httpx, pydantic, typing-extensions, python-dateutil
- **Architecture**: Modular service-based architecture organized by API tags
- **Response Format**: Preserves complete MeOS API response structure
- **Error Handling**: Maps HTTP status codes to appropriate exception types
- **Token Management**: Automatic token refresh with configurable lifetime and threshold

## [0.1.0] - 2024-01-XX

### Added
- Initial release

### Changed
- N/A

### Deprecated
- N/A

### Removed
- N/A

### Fixed
- N/A

### Security
- N/A

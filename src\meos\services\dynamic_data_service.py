"""
Dynamic data service for MeOS Python SDK.

This module provides dynamic property data operations.
Tag: 动态属性数据
"""

import logging
from typing import List, Optional

from ..models.dynamic_data import (
    DynamicHistoryRequest,
    DynamicHistoryResponse,
    DynamicRealtimeRequest,
    DynamicRealtimeResponse,
    DynamicRealtimeWithFormulaRequest,
    DynamicRealtimeWithFormulaResponse,
)
from .base_service import BaseService

logger = logging.getLogger(__name__)


class DynamicDataService(BaseService):
    """Dynamic data service for MeOS API - 动态属性数据."""

    GET_DYNAMIC_HISTORY_ENDPOINT = {"url": "/p/dt/v1/public/ins/dy/history", "method": "POST"}
    GET_DYNAMIC_REALTIME_ENDPOINT = {"url": "/p/dt/v1/public/ins/dy/rt", "method": "POST"}
    GET_DYNAMIC_REALTIME_WITH_FORMULA_ENDPOINT = {"url": "/p/dt/v1/public/ins/dy/rt/fm/calc", "method": "POST"}

    def get_dynamic_history(
        self,
        dataCodes: List[str],
        startTime: str,
        endTime: str,
    ) -> DynamicHistoryResponse:
        """
        获取动态属性历史数据.

        Args:
            data_codes: 属性编码列表
            start_time: 开始时间
            end_time: 结束时间

        Returns:
            完整的API响应, 包含 {code, msg, data} 结构

        Raises:
            MeOSAPIError: If the request fails
        """

        request_data = DynamicHistoryRequest(
            dataCodes=dataCodes,
            startTime=startTime,
            endTime=endTime,
        )
        json_data = self._validate_request_data(request_data)
        endpoint = self.GET_DYNAMIC_HISTORY_ENDPOINT
        response = self._make_request(
            method=endpoint["method"],
            url=endpoint["url"],
            response_model=DynamicHistoryResponse,
            json_data=json_data,
        )

        return response

    async def get_dynamic_history_async(
        self,
        dataCodes: List[str],
        startTime: str,
        endTime: str,
    ) -> DynamicHistoryResponse:
        """
        获取动态属性历史数据 (异步).

        Args:
            data_codes: 属性编码列表
            start_time: 开始时间
            end_time: 结束时间

        Returns:
            完整的API响应, 包含 {code, msg, data} 结构

        Raises:
            MeOSAPIError: If the request fails
        """
        request_data = DynamicHistoryRequest(
            dataCodes=dataCodes,
            startTime=startTime,
            endTime=endTime,
        )
        json_data = self._validate_request_data(request_data)
        endpoint = self.GET_DYNAMIC_HISTORY_ENDPOINT
        response = await self._make_async_request(
            method=endpoint["method"],
            url=endpoint["url"],
            response_model=DynamicHistoryResponse,
            json_data=json_data,
        )

        return response

    def get_dynamic_realtime(
        self,
        dataCodes: List[str],
        timestamp: Optional[str] = None,
    ) -> DynamicRealtimeResponse:
        """
        获取动态属性实时数据.

        Args:
            data_codes: 属性编码列表
            timestamp: 时间戳 (可选)

        Returns:
            完整的API响应, 包含 {code, msg, data} 结构

        Raises:
            MeOSAPIError: If the request fails
        """
        request_data = DynamicRealtimeRequest(
            dataCodes=dataCodes,
        )
        json_data = self._validate_request_data(request_data)

        params = {}
        if timestamp:
            params = self._build_query_params(ts=timestamp)

        endpoint = self.GET_DYNAMIC_REALTIME_ENDPOINT
        response = self._make_request(
            method=endpoint["method"],
            url=endpoint["url"],
            response_model=DynamicRealtimeResponse,
            json_data=json_data,
            params=params,
        )

        return response

    async def get_dynamic_realtime_async(
        self,
        dataCodes: List[str],
        timestamp: Optional[str] = None,
    ) -> DynamicRealtimeResponse:
        """
        获取动态属性实时数据 (异步).

        Args:
            data_codes: 属性编码列表
            timestamp: 时间戳 (可选)

        Returns:
            完整的API响应, 包含 {code, msg, data} 结构

        Raises:
            MeOSAPIError: If the request fails
        """
        request_data = DynamicRealtimeRequest(
            dataCodes=dataCodes,
        )
        json_data = self._validate_request_data(request_data)

        params = {}
        if timestamp:
            params = self._build_query_params(ts=timestamp)

        endpoint = self.GET_DYNAMIC_REALTIME_ENDPOINT
        response = await self._make_async_request(
            method=endpoint["method"],
            url=endpoint["url"],
            response_model=DynamicRealtimeResponse,
            json_data=json_data,
            params=params,
        )

        return response

    def get_dynamic_realtime_with_formula(
        self,
        dataCodes: List[str],
        timestamp: Optional[str] = None,
    ) -> DynamicRealtimeResponse:
        """
        获取动态属性实时数据 (含公式计算) .

        Args:
            data_codes: 属性编码列表
            timestamp: 时间戳 (可选)
            include_formula: 是否包含公式计算

        Returns:
            完整的API响应, 包含 {code, msg, data} 结构

        Raises:
            MeOSAPIError: If the request fails
        """
        request_data = DynamicRealtimeWithFormulaRequest(
            dataCodes=dataCodes,
        )
        json_data = self._validate_request_data(request_data)

        params = {}
        if timestamp:
            params = self._build_query_params(ts=timestamp)

        endpoint = self.GET_DYNAMIC_REALTIME_WITH_FORMULA_ENDPOINT
        response = self._make_request(
            method=endpoint["method"],
            url=endpoint["url"],
            response_model=DynamicRealtimeWithFormulaResponse,
            json_data=json_data,
            params=params,
        )

        return response

    async def get_dynamic_realtime_with_formula_async(
        self,
        dataCodes: List[str],
        timestamp: Optional[str] = None,
    ) -> DynamicRealtimeResponse:
        """
        获取动态属性实时数据 (含公式计算) (异步).

        Args:
            data_codes: 属性编码列表
            timestamp: 时间戳 (可选)
            include_formula: 是否包含公式计算

        Returns:
            完整的API响应, 包含 {code, msg, data} 结构

        Raises:
            MeOSAPIError: If the request fails
        """
        request_data = DynamicRealtimeWithFormulaRequest(
            dataCodes=dataCodes,
        )
        json_data = self._validate_request_data(request_data)

        params = {}
        if timestamp:
            params = self._build_query_params(ts=timestamp)

        endpoint = self.GET_DYNAMIC_REALTIME_WITH_FORMULA_ENDPOINT
        response = await self._make_async_request(
            method=endpoint["method"],
            url=endpoint["url"],
            response_model=DynamicRealtimeWithFormulaResponse,
            json_data=json_data,
            params=params,
        )

        return response

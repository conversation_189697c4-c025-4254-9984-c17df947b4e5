"""
project/station/eq service for MeOS Python SDK.

This module provides Base/Static/Dynamic attribute operations.
"""

import logging
from typing import Optional, Type

from ..base_service import BaseService, R

logger = logging.getLogger(__name__)


class CommonInstanceServiceMixin(BaseService):
    """Common instance service for MeOS API - 公共实例信息."""

    def get_instance_info(self, url: str, *, dataCode: Optional[str], response_model: Type[R]) -> R:
        """
        获取实例详细信息.

        Args:
            dataCode: 数据编码

        Returns:
            完整的API响应, 包含 {code, msg, data} 结构

        Raises:
            MeOSAPIError: If the request fails
        """
        params = self._build_query_params(dataCode=dataCode)

        response = self._make_request(
            method="GET",
            url=url,
            response_model=response_model,
            params=params,
        )

        return response

    async def get_instance_info_async(self, url: str, *, dataCode: Optional[str], response_model: Type[R]) -> R:
        """
        获取实例详细信息 (异步).

        Args:
            dataCode: 数据编码

        Returns:
            完整的API响应, 包含 {code, msg, data} 结构

        Raises:
            MeOSAPIError: If the request fails
        """

        params = self._build_query_params(dataCode=dataCode)

        response = await self._make_async_request(
            method="GET",
            url=url,
            response_model=response_model,
            params=params,
        )

        return response

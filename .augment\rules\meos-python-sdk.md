---
type: "always_apply"
description: "MeOS操作系统接口-基于 MCP 提取的 OpenAPI 数据生成 Python SDK 并使用 Pydantic 建模"
---
# 🧠 提示词: 基于 MCP 提取的 OpenAPI 数据生成 Python SDK 并使用 Pydantic 建模

## 🎯 目标说明:

通过 **MCP 协议调用已实现的功能模块**, 获取目标 OpenAPI 的解析结果.根据返回的接口定义数据, 自动生成对应的 **Python SDK 客户端类封装代码**, 并使用 **Pydantic 模型**对每个接口的请求参数和响应数据进行类型建模与验证.

SDK 应支持 **同步和异步** 调用方式, 具备良好的可读性、可扩展性、类型安全性和错误处理机制.

---

## 🔧 功能需求细化:

### 1. ✅ **通过 MCP 获取 OpenAPI 接口定义数据**
- 使用 MCP 协议调用指定工具, 获取解析后的 OpenAPI 接口定义.
- 解析内容包括但不限于:
  - `paths` (接口路径)
  - `methods` (HTTP 方法)
  - `parameters` (路径、查询、Header 等参数)
  - `requestBody` (请求体结构)
  - `responses` (响应结构及状态码)
  - `operationId` (接口唯一标识符)
  - `tags` (接口分组标签)

### 2. ✅ **生成 Python SDK 客户端类**

#### 类设计要求:
- 主客户端类名格式为: `{OpenAPITitle}Client`
- 支持设置以下配置项:
  - `base_url`: 基础 URL
  - `headers`: 默认请求头 (如认证 token)
  - `timeout`: 请求超时时间
  - `retries`: 请求重试次数 (默认 3 次)
- 使用 `httpx.Client` 和 `httpx.AsyncClient` 分别实现同步和异步接口调用.
- 所有接口方法需具备完整的类型注解 (Type Hints) , 包括输入输出模型.
- 返回值应为标准结构, 例如:
```python
class APIResponse(BaseModel):
    code: int
    msg: str
    data: Optional[Any] = None
```
- 若接口定义中包含多个响应类型 (如 200, 400, 422) , 则 SDK 需分别建模并返回对应结构.

#### 日志记录:
- 使用 Python 标准库 `logging` 记录请求日志 (如 URL、方法、参数、耗时等) .
- 可选日志级别控制 (INFO/DEBUG) .

#### 错误处理:
- 对非 2xx 响应进行统一异常捕获, 抛出 `APIError` 异常或其子类.
- 可自定义异常映射策略 (如 404 映射为 `ResourceNotFoundError`) .

### 3. ✅ **使用 Pydantic 模型建模请求/响应结构**

#### 请求模型:
- 对每个接口的 `parameters` 和 `requestBody` 生成对应的 Pydantic 请求模型类.
- 字段描述应从 OpenAPI 中提取 (如 description、example、required 等) .
- 示例字段可通过 `Field(examples=[...])` 注入.
- 示例:


```python
from typing import Annotated, Dict, Generic, List, Literal, Optional, TypeVar

from pydantic import Field, ValidationInfo, field_validator, model_validator


class _StartEndSchema(BaseModel):
    """起始值计算"""

    start_time: Annotated[Optional[str], Field(None, description="开始时间: 2024-07-01 00:00:00")]
    end_time: Annotated[Optional[str], Field(None, description="结束时间: 2024-07-01 23:00:00")]
    start_value: Annotated[Optional[Number], Field(None, description="起始值")]
    end_value: Annotated[Optional[Number], Field(None, description="结束值")]
    unit_increase: Annotated[Optional[Number], Field(None, description="单位增长值", examples=[100])]
    task_type: Annotated[Literal["manual", "auto"], Field("auto", title="任务类型", examples=["manual", "auto"])]

    @model_validator(mode="after")
    def convert_str_to_datetime(self) -> Self:
        try:
            start_time = datetime.strptime(self.start_time, "%Y-%m-%d %H:%M:%S")
            end_time = datetime.strptime(self.end_time, "%Y-%m-%d %H:%M:%S")
        except Exception:
            raise ValueError("时间格式错误, 请使用 %Y-%m-%d %H:%M:%S 格式")
        else:
            if end_time < start_time:
                raise TimeCheckError("结束时间不能早于开始时间")

            return self

```

#### 响应模型:
- 对每个接口的 `responses` 中每种状态码 (如 200、400、422) 生成相应的 Pydantic 响应模型类.
- 如果接口未定义响应结构, 则使用通用响应类兜底.

#### 自定义校验逻辑:
- 在 Pydantic 模型中加入必要的校验器 (validator/model_validator/field_validator) , 确保参数合法性.
- 示例:

```python
@model_validator(mode="after")
def check_time_range(self):
    ...

@field_validator("collect_frequency")
    @classmethod
    def convert_to_str(cls, v: str) -> str:
```

#### 模型命名规范:
- 请求模型: `{OperationId}Request`
- 成功响应模型: `{OperationId}Response`
- 错误响应模型: `{OperationId}ErrorResponse_{StatusCode}` 或按业务语义命名
- 若无 `operationId`, 则优先使用 `tag` 英文翻译作为前缀, 其次`根据接口描述`生成合适名称.

#### 接口归类管理:
- 按照 OpenAPI 中的 `tags` 进行接口分组, 每个 tag 对应一个子类.
- 子类命名为 `{Tag}API`, 主客户端通过属性引用这些子类.
- 未归属任何 tag 的接口统一放入 `DefaultAPI` 类中.

### 4. ✅ **代码组织与结构建议**

SDK 项目结构建议如下:

```
sdk/
├── client.py          # 主客户端类 + 各子类导入入口
├── models/            # 所有 Pydantic 模型
│   ├── __init__.py
│   ├── user_models.py
│   └── common_models.py
├── services/          # 各子类接口实现
│   ├── __init__.py
│   ├── user_service.py
│   └── common_service.py
├── exceptions.py      # 异常定义
└── utils.py           # 工具函数 (如日志、重试、序列化等)
```

---

## 📁 输入数据格式 (由 MCP 提供)

```json
{
  "title": "SampleAPI",
  "version": "1.0.0",
  "paths": {
    "/users/{userId}": {
      "get": {
        "operationId": "getUserInfo",
        "summary": "获取用户信息",
        "tags": ["User"],
        "parameters": [
          {
            "name": "userId",
            "in": "path",
            "required": true,
            "schema": { "type": "integer" }
          }
        ],
        "responses": {
          "200": {
            "description": "成功响应",
            "content": {
              "application/json": {
                "schema": {
                  "type": "object",
                  "properties": {
                    "id": { "type": "integer" },
                    "name": { "type": "string" },
                    "email": { "type": "string" }
                  }
                }
              }
            },
          "404": {
            "description": "用户不存在",
            "content": {
              "application/json": {
                "schema": {
                  "type": "object",
                  "properties": {
                    "code": { "type": "integer" },
                    "msg": { "type": "string" }
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}
```
---

---

## 📤 输出要求:

请生成一段**完整可用的 Python SDK 代码**, 包含以下内容:

- 主客户端类 (如 `SampleAPIClient`)
- 子类接口封装 (如 `UserAPI`)
- 所有接口使用的 Pydantic 模型类 (请求、响应、错误等)
- 支持同步和异步调用
- 包含详细 docstring、类型注解、日志、异常处理
- 符合 PEP8 规范
- 输出结构必须是完整的 API 响应格式 (如 `{code, msg, data}`)

---

## ✅ 示例输出 (简化示意)

```python
from pydantic import BaseModel
import httpx
import logging

# -----------------------------
# Models
# -----------------------------

class GetUserInfoRequest(BaseModel):
    userId: Annotated[int, Field(description="用户ID", examples=[123])]

class GetUserInfoResponse(BaseModel):
    id: Annotated[int, Field(description="用户ID")]
    name: Annotated[str, Field(description="用户名称")]
    email: Annotated[str, Field(description="用户邮箱")]

class GetUserInfoErrorResponse404(BaseModel):
    code: Annotated[int, Field()]
    msg: Annotated[str, Field()]

# -----------------------------
# Exceptions
# -----------------------------

class APIError(Exception):
    def __init__(self, status_code: int, message: str, response: dict):
        self.status_code = status_code
        self.message = message
        self.response = response
        super().__init__(f"[{status_code}] {message}")

# -----------------------------
# Client
# -----------------------------

class UserAPI:
    def __init__(self, client: httpx.Client):
        self.client = client
        self.logger = logging.getLogger(__name__)

    def get_user_info(self, request: GetUserInfoRequest) -> GetUserInfoResponse | GetUserInfoErrorResponse404:
        url = f"/users/{request.userId}"
        try:
            resp = self.client.get(url)
            if resp.status_code == 200:
                return GetUserInfoResponse(**resp.json())
            else:
                return GetUserInfoErrorResponse404(**resp.json())
        except Exception as e:
            self.logger.error(f"Request failed: {e}")
            raise

class SampleAPIClient:
    def __init__(
        self,
        base_url: str = "https://api.example.com/v1",
        headers: Optional[Dict[str, str]] = None,
        timeout: float = 5.0,
        retries: int = 3
    ):
        self.base_url = base_url
        self.headers = headers or {}
        self.timeout = timeout
        self.retries = retries
        self.sync_client = httpx.Client(base_url=base_url, headers=headers, timeout=timeout)

        # Sub-APIs
        self.user = UserAPI(self.sync_client)

    def close(self):
        self.sync_client.close()
```

---

## 🧩 可选增强功能 (后续 Prompt 扩展方向) :

- 自动生成异步接口 (AsyncClient)
- 支持 OAuth2/Bearer Token 自动刷新
- 自动生成单元测试 (pytest + pytest-httpx)
- 支持接口分页自动遍历
- 支持多环境配置 (dev/test/prod)
- 自动生成 README 文档和使用示例

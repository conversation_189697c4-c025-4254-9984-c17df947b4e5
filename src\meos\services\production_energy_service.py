"""
Production energy service for MeOS Python SDK.

This module provides production energy system operations.
Tag: 生产能耗信息
"""

import logging
from typing import Optional

from meos.services.common import CommonAttributeServiceMixin, CommonInstanceServiceMixin

from ..models.instances import (
    CommonBaseInfoListRequest,
    CommonBaseInfoListResponse,
    CommonDynamicInfoListRequest,
    CommonDynamicPropertyListResponse,
    CommonStaticInfoListRequest,
    CommonStaticPropertyListResponse,
    ProductionEnergyPageResponse,
    ProductionEnergyResponse,
    ProductionEnergyTreeResponse,
)

logger = logging.getLogger(__name__)


class ProductionEnergyService(CommonAttributeServiceMixin, CommonInstanceServiceMixin):
    """Production energy service for MeOS API - 生产能耗信息."""

    PCM_INFO_ENDPOINT = "/p/dt/v1/public/pcm/info"
    PCM_TREE_ENDPOINT = {"url": "/p/dt/v1/public/pcm/tree", "method": "GET"}
    PCM_PAGE_ENDPOINT = {"url": "/p/dt/v1/public/pcm/page", "method": "GET"}
    BASE_PROPERTIES_ENDPOINT = "/p/dt/v1/public/pcm/page/base"
    DYNAMIC_PROPERTIES_ENDPOINT = "/p/dt/v1/public/pcm/page/dynamic"
    STATIC_PROPERTIES_ENDPOINT = "/p/dt/v1/public/pcm/page/static"

    def get_pcm_info(self, dataCode: Optional[str] = None) -> ProductionEnergyResponse:
        """
        获取生产能耗系统详细信息.

        Args:
            dataCode: 生产能耗系统编码

        Returns:
            完整的API响应, 包含 {code, msg, data} 结构

        Raises:
            MeOSAPIError: If the request fails
        """
        return self.get_instance_info(self.PCM_INFO_ENDPOINT, dataCode=dataCode, response_model=ProductionEnergyResponse)

    async def get_pcm_info_async(self, dataCode: Optional[str] = None) -> ProductionEnergyResponse:
        """
        获取生产能耗系统详细信息 (异步).

        Args:
            dataCode: 生产能耗系统编码

        Returns:
            完整的API响应, 包含 {code, msg, data} 结构

        Raises:
            MeOSAPIError: If the request fails
        """
        return await self.get_instance_info_async(self.PCM_INFO_ENDPOINT, dataCode=dataCode, response_model=ProductionEnergyResponse)

    def get_pcm_tree(self, dataCode: str) -> ProductionEnergyTreeResponse:
        """
        查询生产能耗系统所有下级 (树).

        Args:
            dataCode: 生产能耗系统编码
        Returns:
            完整的API响应, 包含 {code, msg, data} 结构
        Raises:
            MeOSAPIError: If the request fails
        """
        params = self._build_query_params(dataCode=dataCode)
        response = self._make_request(
            method=self.PCM_TREE_ENDPOINT["method"],
            url=self.PCM_TREE_ENDPOINT["url"],
            response_model=ProductionEnergyTreeResponse,
            params=params,
        )
        return response

    async def get_pcm_tree_async(self, dataCode: str) -> ProductionEnergyTreeResponse:
        """
        查询生产能耗系统所有下级 (树) (异步).

        Args:
            dataCode: 生产能耗系统编码
        Returns:
            完整的API响应, 包含 {code, msg, data} 结构
        Raises:
            MeOSAPIError: If the request fails
        """
        params = self._build_query_params(dataCode=dataCode)
        response = await self._make_async_request(
            method=self.PCM_TREE_ENDPOINT["method"],
            url=self.PCM_TREE_ENDPOINT["url"],
            response_model=ProductionEnergyTreeResponse,
            params=params,
        )
        return response

    def get_pcm_page(self, dataCode: str, *, pageNumber: int = 1, pageSize: int = 10) -> ProductionEnergyPageResponse:
        """
        获取项目下生产能耗系统（分页）

        Args:
            dataCode: 项目编码
            pageNumber: 页码, 默认1
            pageSize: 每页条数, 默认10
        Returns:
            完整的API响应, 包含 {code, msg, data} 结构
        Raises:
            MeOSAPIError: If the request fails
        """
        params = self._build_query_params(dataCode=dataCode, pageNumber=pageNumber, pageSize=pageSize)
        response = self._make_request(
            method=self.PCM_PAGE_ENDPOINT["method"],
            url=self.PCM_PAGE_ENDPOINT["url"],
            response_model=ProductionEnergyPageResponse,
            params=params,
        )
        return response

    async def get_pcm_page_async(self, dataCode: str, *, pageNumber: int = 1, pageSize: int = 10) -> ProductionEnergyPageResponse:
        """
        获取项目下生产能耗系统（分页）(异步)

        Args:
            dataCode: 项目编码
            pageNumber: 页码, 默认1
            pageSize: 每页条数, 默认10
        Returns:
            完整的API响应, 包含 {code, msg, data} 结构
        Raises:
            MeOSAPIError: If the request fails
        """
        params = self._build_query_params(dataCode=dataCode, pageNumber=pageNumber, pageSize=pageSize)
        response = await self._make_async_request(
            method=self.PCM_PAGE_ENDPOINT["method"],
            url=self.PCM_PAGE_ENDPOINT["url"],
            response_model=ProductionEnergyPageResponse,
            params=params,
        )
        return response

    def get_base_properties(
        self,
        req: CommonBaseInfoListRequest,
    ) -> CommonBaseInfoListResponse:
        """
        获取基础属性列表.

        Args:
            req: CommonBaseInfoListRequest
        Returns:
            完整的API响应, 包含 {code, msg, data} 结构
        Raises:
            MeOSAPIError: If the request fails
        """
        return self.get_common_base_properties(self.BASE_PROPERTIES_ENDPOINT, req)

    async def get_base_properties_async(
        self,
        req: CommonBaseInfoListRequest,
    ) -> CommonBaseInfoListResponse:
        """
        获取基础属性列表 (异步).

        Args:
            req: CommonBaseInfoListRequest
        Returns:
            完整的API响应, 包含 {code, msg, data} 结构
        Raises:
            MeOSAPIError: If the request fails
        """
        return await self.get_common_base_properties_async(self.BASE_PROPERTIES_ENDPOINT, req)

    def get_dynamic_properties(
        self,
        req: CommonDynamicInfoListRequest,
    ) -> CommonDynamicPropertyListResponse:
        """
        获取动态属性列表.

        Args:
            req: CommonDynamicInfoListRequest
        Returns:
            完整的API响应, 包含 {code, msg, data} 结构
        Raises:
            MeOSAPIError: If the request fails
        """
        return self.get_common_dynamic_properties(self.DYNAMIC_PROPERTIES_ENDPOINT, req)

    async def get_dynamic_properties_async(
        self,
        req: CommonDynamicInfoListRequest,
    ) -> CommonDynamicPropertyListResponse:
        """
        获取动态属性列表 (异步).

        Args:
            req: CommonDynamicInfoListRequest
        Returns:
            完整的API响应, 包含 {code, msg, data} 结构
        Raises:
            MeOSAPIError: If the request fails
        """
        return await self.get_common_dynamic_properties_async(self.DYNAMIC_PROPERTIES_ENDPOINT, req)

    def get_static_properties(
        self,
        req: CommonStaticInfoListRequest,
    ) -> CommonStaticPropertyListResponse:
        """
        获取静态属性列表.

        Args:
            req: CommonStaticInfoListRequest
        Returns:
            完整的API响应, 包含 {code, msg, data} 结构
        Raises:
            MeOSAPIError: If the request fails
        """
        return self.get_common_static_properties(self.STATIC_PROPERTIES_ENDPOINT, req)

    async def get_static_properties_async(
        self,
        req: CommonStaticInfoListRequest,
    ) -> CommonStaticPropertyListResponse:
        """
        获取静态属性列表 (异步).

        Args:
            req: CommonStaticInfoListRequest
        Returns:
            完整的API响应, 包含 {code, msg, data} 结构
        Raises:
            MeOSAPIError: If the request fails
        """
        return await self.get_common_static_properties_async(self.STATIC_PROPERTIES_ENDPOINT, req)

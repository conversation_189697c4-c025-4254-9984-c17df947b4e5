"""
Message service for MeOS Python SDK.

This module provides message bus operations.
"""

import logging
from typing import List

from ..models.base import BaseResponse
from ..models.common import MessageItem, MessageSendRequest
from .base_service import BaseService

logger = logging.getLogger(__name__)


class MessageService(BaseService):
    """Message service for MeOS API - 消息总线."""

    SEND_MESSAGE_ENDPOINT = {"url": "/p/mbus/message/send", "method": "POST"}

    def send_message(
        self,
        service_id: str,
        service_secret: str,
        topic_id: str,
        messages: List[MessageItem],
        is_forward: bool = True,
    ) -> BaseResponse[None]:
        """
        发送消息.

        消息总线发送消息接口, 需要配合MeOS工具使用。使用前先在消息总线中配置, 然后再调用。

        Args:
            service_id: 生产者服务【服务ID】, 在消息总线【服务管理】->【生产者服务管理】->【服务ID】中获取
            service_secret: 生产者服务【服务secret】, 在消息总线【服务管理】->【生产者服务管理】->【服务secret】中获取
            topic_id: 消息主题调用ID, 从消息总线【消息主题管理】->【消息主题管理】->【Tpopic调用ID】
            messages: 消息内容项列表, 类型为List[MessageItem], 每项需严格匹配OpenAPI结构
            is_forward: 是否转发消息, true:消息总线会使用内部机制将消息转发给消费者; false:消息总线只将消息发送到对应的中间件, 不做转发, 默认true

        Returns:
            完整的API响应, 包含 {code, msg, data} 结构, data恒为None

        Raises:
            MeOSAPIError: If the request fails

        Example:
            >>> message_service = MessageService(http_client)
            >>> messages = [
            ...     MessageItem(
            ...         is_delay=False,
            ...         message_id="83b5a343-f778-415c-9b36-46b5edb3c01f",
            ...         content={"name": "赵六", "age": 18}
            ...     )
            ... ]
            >>> response = message_service.send_message(
            ...     service_id="1828735305230323714",
            ...     service_secret="2fd2cdee21a04aa2b474942a5927b013",
            ...     topic_id="9a19079506b148dca5a2176b9d94baa5",
            ...     messages=messages
            ... )
        """
        headers = {
            "service_id": service_id,
            "service_secret": service_secret,
        }

        request_model = MessageSendRequest(
            topic_id=topic_id,
            is_forward=str(is_forward).lower(),
            messages=messages,
        )
        request_data = self._validate_request_data(request_model)

        endpoint = self.SEND_MESSAGE_ENDPOINT
        response = self._make_request(
            method=endpoint["method"],
            url=endpoint["url"],
            response_model=BaseResponse[None],
            json_data=request_data,
            headers=headers,
        )

        return response

    # Async methods
    async def asend_message(
        self,
        service_id: str,
        service_secret: str,
        topic_id: str,
        messages: List[MessageItem],
        is_forward: bool = True,
    ) -> BaseResponse[None]:
        """
        发送消息 (异步)。

        消息总线发送消息接口, 需要配合MeOS工具使用。使用前先在消息总线中配置, 然后再调用。

        Args:
            service_id: 生产者服务【服务ID】, 在消息总线【服务管理】->【生产者服务管理】->【服务ID】中获取
            service_secret: 生产者服务【服务secret】, 在消息总线【服务管理】->【生产者服务管理】->【服务secret】中获取
            topic_id: 消息主题调用ID, 从消息总线【消息主题管理】->【消息主题管理】->【Tpopic调用ID】
            messages: 消息内容项列表, 类型为List[MessageItem], 每项需严格匹配OpenAPI结构
            is_forward: 是否转发消息, true:消息总线会使用内部机制将消息转发给消费者; false:消息总线只将消息发送到对应的中间件, 不做转发, 默认true

        Returns:
            完整的API响应, 包含 {code, msg, data} 结构, data恒为None

        Raises:
            MeOSAPIError: If the request fails

        Example:
            >>> message_service = MessageService(http_client)
            >>> messages = [
            ...     MessageItem(
            ...         is_delay=False,
            ...         message_id="83b5a343-f778-415c-9b36-46b5edb3c01f",
            ...         content={"name": "赵六", "age": 18}
            ...     )
            ... ]
            >>> response = await message_service.asend_message(
            ...     service_id="1828735305230323714",
            ...     service_secret="2fd2cdee21a04aa2b474942a5927b013",
            ...     topic_id="9a19079506b148dca5a2176b9d94baa5",
            ...     messages=messages
            ... )
        """
        headers = {
            "service_id": service_id,
            "service_secret": service_secret,
        }

        request_model = MessageSendRequest(
            topic_id=topic_id,
            is_forward=str(is_forward).lower(),
            messages=messages,
        )
        request_data = self._validate_request_data(request_model)

        endpoint = self.SEND_MESSAGE_ENDPOINT
        response = await self._make_async_request(
            method=endpoint["method"],
            url=endpoint["url"],
            response_model=BaseResponse[None],
            json_data=request_data,
            headers=headers,
        )

        return response

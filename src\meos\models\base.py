"""
Base model classes for MeOS Python SDK.

This module provides the foundation classes for all data models.
"""

from datetime import datetime
from typing import Generic, List, Optional, TypeVar, Union

from pydantic import BaseModel as PydanticBaseModel
from pydantic import ConfigDict, Field

T = TypeVar("T")


class BaseModel(PydanticBaseModel):
    """Base model class with common configuration."""

    model_config = ConfigDict(
        # Allow extra fields for forward compatibility
        extra="allow",
        # Use enum values instead of enum objects
        use_enum_values=True,
        # Validate assignment
        validate_assignment=True,
        # Allow population by field name or alias
        populate_by_name=True,
        # Serialize by alias
        ser_by_alias=True,
    )


class BaseResponse(BaseModel, Generic[T]):
    """Base response model for MeOS API responses."""

    code: int = Field(..., description="响应状态码")
    msg: str = Field(..., description="响应消息")
    data: Optional[T] = Field(None, description="响应数据")

    @property
    def is_success(self) -> bool:
        """Check if the response indicates success."""
        return self.code == 200

    @property
    def is_error(self) -> bool:
        """Check if the response indicates an error."""
        return self.code != 200


class PageInfo(BaseModel):
    """Pagination information."""

    total: Optional[int] = Field(None, description="总记录数")
    size: Optional[int] = Field(None, description="每页大小")
    current: Optional[int] = Field(None, description="当前页码")
    pages: Optional[int] = Field(None, description="总页数")

    @property
    def has_next(self) -> bool:
        """Check if there is a next page."""
        return self.current < self.pages

    @property
    def has_previous(self) -> bool:
        """Check if there is a previous page."""
        return self.current > 1


class PageResponse(BaseModel, Generic[T]):
    """Paginated response model."""

    records: Optional[List[T]] = Field(None, description="记录列表")
    total: Optional[int] = Field(None, description="总记录数")
    size: Optional[int] = Field(None, description="每页大小")
    current: Optional[int] = Field(None, description="当前页码")
    pages: Optional[int] = Field(None, description="总页数")

    @property
    def page_info(self) -> PageInfo:
        """Get pagination information."""
        return PageInfo(
            current=self.current,
            size=self.size,
            total=self.total,
            pages=self.pages,
        )

    @property
    def has_next(self) -> bool:
        """Check if there is a next page."""
        return self.current < self.pages

    @property
    def has_previous(self) -> bool:
        """Check if there is a previous page."""
        return self.current > 1


class TreeNode(BaseModel, Generic[T]):
    """Generic tree node model."""

    id: str = Field(..., description="节点ID")
    name: str = Field(..., description="节点名称")
    parent_id: Optional[str] = Field(None, description="父节点ID")
    level: int = Field(0, description="节点层级")
    data: Optional[T] = Field(None, description="节点数据")
    children: List["TreeNode[T]"] = Field(default_factory=list, description="子节点")

    @property
    def is_root(self) -> bool:
        """Check if this is a root node."""
        return self.parent_id is None

    @property
    def is_leaf(self) -> bool:
        """Check if this is a leaf node."""
        return len(self.children) == 0

    @property
    def has_children(self) -> bool:
        """Check if this node has children."""
        return len(self.children) > 0

    def find_child(self, child_id: str) -> Optional["TreeNode[T]"]:
        """Find a direct child by ID."""
        for child in self.children:
            if child.id == child_id:
                return child
        return None

    def find_descendant(self, node_id: str) -> Optional["TreeNode[T]"]:
        """Find a descendant node by ID (recursive search)."""
        if self.id == node_id:
            return self

        for child in self.children:
            result = child.find_descendant(node_id)
            if result:
                return result

        return None

    def get_all_descendants(self) -> List["TreeNode[T]"]:
        """Get all descendant nodes."""
        descendants = []
        for child in self.children:
            descendants.append(child)
            descendants.extend(child.get_all_descendants())
        return descendants

    def get_path_to_root(self) -> List[str]:
        """Get the path from this node to the root (list of IDs)."""
        path = [self.id]
        if self.parent_id:
            # Note: This requires the parent node to be accessible
            # In practice, you might need to pass the tree structure
            pass
        return path


class PropertyValue(BaseModel):
    """Property value model."""

    property_code: str = Field(..., description="属性编码")
    property_name: Optional[str] = Field(None, description="属性名称")
    value: Optional[Union[str, int, float, bool]] = Field(None, description="属性值")
    unit: Optional[str] = Field(None, description="单位")
    data_type: Optional[str] = Field(None, description="数据类型")
    timestamp: Optional[datetime] = Field(None, description="时间戳")
    quality: Optional[str] = Field(None, description="数据质量")

    @property
    def is_numeric(self) -> bool:
        """Check if the value is numeric."""
        return isinstance(self.value, (int, float))

    @property
    def is_boolean(self) -> bool:
        """Check if the value is boolean."""
        return isinstance(self.value, bool)

    @property
    def is_string(self) -> bool:
        """Check if the value is string."""
        return isinstance(self.value, str)

    @property
    def numeric_value(self) -> Optional[float]:
        """Get the numeric value."""
        if self.is_numeric:
            return float(self.value)
        return None


class ErrorDetail(BaseModel):
    """Error detail model."""

    field: Optional[str] = Field(None, description="错误字段")
    message: str = Field(..., description="错误消息")
    code: Optional[str] = Field(None, description="错误代码")


class ValidationError(BaseModel):
    """Validation error model."""

    message: str = Field(..., description="错误消息")
    errors: List[ErrorDetail] = Field(default_factory=list, description="详细错误")


# Update forward references
TreeNode.model_rebuild()

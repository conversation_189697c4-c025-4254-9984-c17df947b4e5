"""Project template service for MeOS Python SDK.

This module provides project template operations.
Tag: 项目模板信息
"""

import logging
from typing import Optional

from meos.services.common.attribute_service import CommonAttributeServiceMixin
from meos.services.common.instance_info_service import CommonInstanceServiceMixin

from ..models.instances import (
    CommonBaseInfoListRequest,
    CommonBaseInfoListResponse,
    CommonDynamicInfoListRequest,
    CommonDynamicPropertyListResponse,
    CommonStaticInfoListRequest,
    CommonStaticPropertyListResponse,
    InstanceTreeResponse,
    ProjectInstanceResponse,
)

logger = logging.getLogger(__name__)


class ProjectTemplateService(CommonAttributeServiceMixin, CommonInstanceServiceMixin):
    """Project template service for MeOS API - 项目模板信息."""

    PROJECT_MODEL_INFO_ENDPOINT = "/p/dt/v1/public/model/project/info"
    BASE_PROPERTIES_ENDPOINT = "/p/dt/v1/public/model/project/page/base"
    DYNAMIC_PROPERTIES_ENDPOINT = "/p/dt/v1/public/model/project/page/dynamic"
    STATIC_PROPERTIES_ENDPOINT = "/p/dt/v1/public/model/project/page/static"
    LIST_PROJECT_MODELS_ENDPOINT = "/p/dt/v1/public/model/project/ins/list"
    PROJECT_MODEL_TREE_ENDPOINT = "/p/dt/v1/public/model/project/ins/tree"

    def get_project_model_info(self, dataCode: Optional[str] = None) -> ProjectInstanceResponse:
        """
        获取项目模型详细信息.
        Args:
            dataCode: 项目编码
        Returns:
            完整的API响应, 包含 {code, msg, data} 结构
        Raises:
            MeOSAPIError: If the request fails
        """
        return self.get_instance_info(
            self.PROJECT_MODEL_INFO_ENDPOINT, dataCode=dataCode, response_model=ProjectInstanceResponse
        )

    async def get_project_model_info_async(self, dataCode: Optional[str] = None) -> ProjectInstanceResponse:
        """
        获取项目模型详细信息 (异步).
        Args:
            dataCode: 项目编码
        Returns:
            完整的API响应, 包含 {code, msg, data} 结构
        Raises:
            MeOSAPIError: If the request fails
        """
        return await self.get_instance_info_async(
            self.PROJECT_MODEL_INFO_ENDPOINT, dataCode=dataCode, response_model=ProjectInstanceResponse
        )

    def list_project_models(self) -> InstanceTreeResponse:
        """
        查询项目模型列表.
        Returns:
            完整的API响应, 包含 {code, msg, data} 结构
        Raises:
            MeOSAPIError: If the request fails
        """
        return self.get_instance_info(
            self.LIST_PROJECT_MODELS_ENDPOINT, dataCode=None, response_model=InstanceTreeResponse
        )

    async def list_project_models_async(self) -> InstanceTreeResponse:
        """
        查询项目模型列表 (异步).
        Returns:
            完整的API响应, 包含 {code, msg, data} 结构
        Raises:
            MeOSAPIError: If the request fails
        """
        return await self.get_instance_info_async(
            self.LIST_PROJECT_MODELS_ENDPOINT, dataCode=None, response_model=InstanceTreeResponse
        )

    def get_project_model_tree(self, dataCode: str) -> InstanceTreeResponse:
        """
        查询项目模型所有下级 (树) .
        Args:
            dataCode: 项目编码 (必填)
        Returns:
            完整的API响应, 包含 {code, msg, data} 结构
        Raises:
            MeOSAPIError: If the request fails
        """
        return self.get_instance_info(
            self.PROJECT_MODEL_TREE_ENDPOINT, dataCode=dataCode, response_model=InstanceTreeResponse
        )

    async def get_project_model_tree_async(self, dataCode: str) -> InstanceTreeResponse:
        """
        查询项目模型所有下级 (树)  (异步).
        Args:
            dataCode: 项目编码 (必填)
        Returns:
            完整的API响应, 包含 {code, msg, data} 结构
        Raises:
            MeOSAPIError: If the request fails
        """
        return await self.get_instance_info_async(
            self.PROJECT_MODEL_TREE_ENDPOINT, dataCode=dataCode, response_model=InstanceTreeResponse
        )

    def get_base_properties(self, req: CommonBaseInfoListRequest) -> CommonBaseInfoListResponse:
        """
        获取基础属性列表.
        Args:
            req: CommonBaseInfoListRequest
        Returns:
            完整的API响应, 包含 {code, msg, data} 结构
        Raises:
            MeOSAPIError: If the request fails
        """
        return self.get_common_base_properties(self.BASE_PROPERTIES_ENDPOINT, req)

    async def get_base_properties_async(self, req: CommonBaseInfoListRequest) -> CommonBaseInfoListResponse:
        """
        获取基础属性列表 (异步).
        Args:
            req: CommonBaseInfoListRequest
        Returns:
            完整的API响应, 包含 {code, msg, data} 结构
        Raises:
            MeOSAPIError: If the request fails
        """
        return await self.get_common_base_properties_async(self.BASE_PROPERTIES_ENDPOINT, req)

    def get_dynamic_properties(self, req: CommonDynamicInfoListRequest) -> CommonDynamicPropertyListResponse:
        """
        获取动态属性列表.
        Args:
            req: CommonDynamicInfoListRequest
        Returns:
            完整的API响应, 包含 {code, msg, data} 结构
        Raises:
            MeOSAPIError: If the request fails
        """
        return self.get_common_dynamic_properties(self.DYNAMIC_PROPERTIES_ENDPOINT, req)

    async def get_dynamic_properties_async(
        self, req: CommonDynamicInfoListRequest
    ) -> CommonDynamicPropertyListResponse:
        """
        获取动态属性列表 (异步).
        Args:
            req: CommonDynamicInfoListRequest
        Returns:
            完整的API响应, 包含 {code, msg, data} 结构
        Raises:
            MeOSAPIError: If the request fails
        """
        return await self.get_common_dynamic_properties_async(self.DYNAMIC_PROPERTIES_ENDPOINT, req)

    def get_static_properties(self, req: CommonStaticInfoListRequest) -> CommonStaticPropertyListResponse:
        """
        获取静态属性列表.
        Args:
            req: CommonStaticInfoListRequest
        Returns:
            完整的API响应, 包含 {code, msg, data} 结构
        Raises:
            MeOSAPIError: If the request fails
        """
        return self.get_common_static_properties(self.STATIC_PROPERTIES_ENDPOINT, req)

    async def get_static_properties_async(self, req: CommonStaticInfoListRequest) -> CommonStaticPropertyListResponse:
        """
        获取静态属性列表 (异步).
        Args:
            req: CommonStaticInfoListRequest
        Returns:
            完整的API响应, 包含 {code, msg, data} 结构
        Raises:
            MeOSAPIError: If the request fails
        """
        return await self.get_common_static_properties_async(self.STATIC_PROPERTIES_ENDPOINT, req)

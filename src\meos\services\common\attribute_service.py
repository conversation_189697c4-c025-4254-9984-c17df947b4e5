"""
Base/Static/Dynamic service for MeOS Python SDK.

This module provides Base/Static/Dynamic attribute operations.
"""

import logging
from typing import Type

from pydantic import BaseModel

from meos.models.instances import (
    CommonBaseInfoListRequest,
    CommonBaseInfoListResponse,
    CommonDynamicInfoListRequest,
    CommonDynamicPropertyListResponse,
    CommonStaticInfoListRequest,
    CommonStaticPropertyListResponse,
)

from ..base_service import BaseService, R

logger = logging.getLogger(__name__)


class CommonAttributeServiceMixin(BaseService):
    """Common attribute instance service for MeOS API - 公共属性信息."""

    def _base_request(self, url: str, *, request: BaseModel, response_model: Type[R]) -> R:
        """
        获取基础/动态/静态属性列表.

        Args:
            url (str): The API endpoint route to request.
            req (BaseModel): The request body to request.
        Returns:
            完整的API响应, 包含 {code, msg, data} 结构
        Raises:
            MeOSAPIError: If the request fails
        """

        json_data = self._validate_request_data(request)
        response = self._make_request(
            method="POST",
            url=url,
            response_model=response_model,
            json_data=json_data,
        )
        return response

    async def _base_request_async(self, url: str, *, request: BaseModel, response_model: Type[R]) -> R:
        """
        异步: 获取基础/动态/静态属性列表.

        Args:
            url (str): The API endpoint route to request.
            req (BaseModel): The request body to request.
        Returns:
            完整的API响应, 包含 {code, msg, data} 结构
        Raises:
            MeOSAPIError: If the request fails
        """

        json_data = self._validate_request_data(request)
        response = await self._make_async_request(
            method="POST",
            url=url,
            response_model=response_model,
            json_data=json_data,
        )
        return response

    def get_common_base_properties(
        self,
        url: str,
        req: CommonBaseInfoListRequest,
    ) -> CommonBaseInfoListResponse:
        """
        获取基础属性列表.

        Args:
            url (str): The API endpoint route to request.
            req (CommonBaseInfoListRequest): The request body to request.
        Returns:
            完整的API响应, 包含 {code, msg, data} 结构
        Raises:
            MeOSAPIError: If the request fails
        """

        return self._base_request(url, request=req, response_model=CommonBaseInfoListResponse)

    async def get_common_base_properties_async(
        self,
        url: str,
        req: CommonBaseInfoListRequest,
    ) -> CommonBaseInfoListResponse:
        """
        获取基础属性列表 (异步).

        Args:
            url (str): The API endpoint route to request.
            req: CommonBaseInfoListRequest

        Returns:
            完整的API响应, 包含 {code, msg, data} 结构

        Raises:
            MeOSAPIError: If the request fails
        """

        return await self._base_request_async(url, request=req, response_model=CommonBaseInfoListResponse)

    def get_common_dynamic_properties(
        self,
        url: str,
        req: CommonDynamicInfoListRequest,
    ) -> CommonDynamicPropertyListResponse:
        """
        获取动态属性列表.

        Args:
            url (str): The API endpoint route to request.
            req: CommonDynamicInfoListRequest
        Returns:
            完整的API响应, 包含 {code, msg, data} 结构
        Raises:
            MeOSAPIError: If the request fails
        """

        return self._base_request(url, request=req, response_model=CommonDynamicPropertyListResponse)

    async def get_common_dynamic_properties_async(
        self,
        url: str,
        req: CommonDynamicInfoListRequest,
    ) -> CommonDynamicPropertyListResponse:
        """
        获取动态属性列表 (异步).

        Args:
            url (str): The API endpoint route to request.
            req: CommonDynamicInfoListRequest

        Returns:
            完整的API响应, 包含 {code, msg, data} 结构

        Raises:
            MeOSAPIError: If the request fails
        """

        return await self._base_request_async(url, request=req, response_model=CommonDynamicPropertyListResponse)

    def get_common_static_properties(
        self,
        url: str,
        req: CommonStaticInfoListRequest,
    ) -> CommonStaticPropertyListResponse:
        """
        获取静态属性列表.

        Args:
            url (str): The API endpoint route to request.
            req: CommonStaticInfoListRequest
        Returns:
            完整的API响应, 包含 {code, msg, data} 结构
        Raises:
            MeOSAPIError: If the request fails
        """

        return self._base_request(url, request=req, response_model=CommonStaticPropertyListResponse)

    async def get_common_static_properties_async(
        self,
        url: str,
        req: CommonStaticInfoListRequest,
    ) -> CommonStaticPropertyListResponse:
        """
        获取静态属性列表 (异步).

        Args:
            url (str): The API endpoint route to request.
            req: CommonStaticInfoListRequest

        Returns:
            完整的API响应, 包含 {code, msg, data} 结构

        Raises:
            MeOSAPIError: If the request fails
        """

        return await self._base_request_async(url, request=req, response_model=CommonStaticPropertyListResponse)

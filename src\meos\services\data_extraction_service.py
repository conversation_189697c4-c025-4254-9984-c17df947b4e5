"""
Data extraction service for MeOS Python SDK.

This module provides data extraction operations.
"""

import logging

from ..models.common import DwDeviceDataExtractionRequest, DwDeviceDataExtractionResponse
from .base_service import BaseService

logger = logging.getLogger(__name__)


class DataExtractionService(BaseService):
    """Data extraction service for MeOS API - 数据提取."""

    EXTRACT_DEVICE_DATA_ENDPOINT = {"url": "/p/dw/dw_common/data-extraction", "method": "POST"}

    def extract_device_data(
        self,
        request: DwDeviceDataExtractionRequest,
    ) -> DwDeviceDataExtractionResponse:
        """
        单项目下任意设备数据获取.

        Args:
            request: 请求体, DwDeviceDataExtractionRequest 实例

        Returns:
            DwDeviceDataExtractionResponse, 包含 code, msg, data(total, list)

        Raises:
            MeOSAPIError: If the request fails

        Example:
            >>> data_service = DataExtractionService(http_client)
            >>> req = DwDeviceDataExtractionRequest(
            ...     projectCode="PGY02013_S0000000_ST00000000_U00000000_EQ000000000000_MP0000000",
            ...     interval="15m",
            ...     stime="2025-01-01 14:39:56",
            ...     etime="2025-02-28 14:39:56",
            ...     sysCode="PGY02013_SPD01002_ST00000000_U00000000_EQ000000000000_MP0000000",
            ...     stationCode="",
            ...     unitCode="",
            ...     devCode="",
            ...     current=1,
            ...     size=10
            ... )
            >>> resp = data_service.extract_device_data(req)
        """
        request_data = self._validate_request_data(request)
        endpoint = self.EXTRACT_DEVICE_DATA_ENDPOINT
        response = self._make_request(
            method=endpoint["method"],
            url=endpoint["url"],
            response_model=DwDeviceDataExtractionResponse,
            json_data=request_data,
        )
        return response

    # Async methods
    async def aextract_device_data(
        self,
        request: DwDeviceDataExtractionRequest,
    ) -> DwDeviceDataExtractionResponse:
        """
        单项目下任意设备数据获取 (异步).

        Args:
            request: 请求体, DwDeviceDataExtractionRequest 实例, 字段严格匹配OpenAPI

        Returns:
            DwDeviceDataExtractionResponse, 包含 code, msg, data(total, list)

        Raises:
            MeOSAPIError: If the request fails
        """
        request_data = self._validate_request_data(request)
        endpoint = self.EXTRACT_DEVICE_DATA_ENDPOINT
        response = await self._make_async_request(
            method=endpoint["method"],
            url=endpoint["url"],
            response_model=DwDeviceDataExtractionResponse,
            json_data=request_data,
        )
        return response

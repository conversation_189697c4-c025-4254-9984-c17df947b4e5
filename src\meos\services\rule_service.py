"""
Rule service for MeOS Python SDK.

This module provides rule query operations.
"""

import logging

from ..models.common import (
    FindByConditionsRequest,
    FindByConditionsResponse,
    FindByDeviceNameRequest,
    FindByDeviceNameResponse,
    FindBySysNameAndStationNameRequest,
    FindBySysNameAndStationNameResponse,
    FindBySysNameRequest,
    FindBySysNameResponse,
    FindExactBySysNameAndStationNameRequest,
    FindExactBySysNameAndStationNameResponse,
)
from .base_service import BaseService

logger = logging.getLogger(__name__)


class RuleService(BaseService):
    """Rule service for MeOS API - 规则查询。"""

    FIND_BY_SYS_NAME_ENDPOINT = {"url": "/p/rule/energyModule/findBySysName", "method": "GET"}
    FIND_BY_SYS_NAME_AND_STATION_NAME_ENDPOINT = {
        "url": "/p/rule/energyModule/findBySysNameAndStationName",
        "method": "GET",
    }
    FIND_EXACT_BY_SYS_NAME_AND_STATION_NAME_ENDPOINT = {
        "url": "/p/rule/energyModule/findExactBySysNameAndStationName",
        "method": "GET",
    }
    FIND_BY_DEVICE_NAME_ENDPOINT = {"url": "/p/rule/energyModule/findByDeviceName", "method": "GET"}
    FIND_BY_CONDITIONS_ENDPOINT = {"url": "/p/rule/energyModule/findByConditions", "method": "GET"}

    def find_by_sys_name(self, request: FindBySysNameRequest) -> FindBySysNameResponse:
        """
        根据系统名称模糊查询规则
        """
        params = self._validate_request_data(request)
        endpoint = self.FIND_BY_SYS_NAME_ENDPOINT
        response = self._make_request(
            method=endpoint["method"],
            url=endpoint["url"],
            response_model=FindBySysNameResponse,
            params=params,
        )
        return response

    def find_by_sys_name_and_station_name(
        self, request: FindBySysNameAndStationNameRequest
    ) -> FindBySysNameAndStationNameResponse:
        """
        根据系统名称和站名称模糊查询
        """
        params = self._validate_request_data(request)
        endpoint = self.FIND_BY_SYS_NAME_AND_STATION_NAME_ENDPOINT
        response = self._make_request(
            method=endpoint["method"],
            url=endpoint["url"],
            response_model=FindBySysNameAndStationNameResponse,
            params=params,
        )
        return response

    def find_exact_by_sys_name_and_station_name(
        self, request: FindExactBySysNameAndStationNameRequest
    ) -> FindExactBySysNameAndStationNameResponse:
        """
        根据系统名称和站名称精确查询
        """
        params = self._validate_request_data(request)
        endpoint = self.FIND_EXACT_BY_SYS_NAME_AND_STATION_NAME_ENDPOINT
        response = self._make_request(
            method=endpoint["method"],
            url=endpoint["url"],
            response_model=FindExactBySysNameAndStationNameResponse,
            params=params,
        )
        return response

    def find_by_device_name(self, request: FindByDeviceNameRequest) -> FindByDeviceNameResponse:
        """
        根据设备名称模糊查询
        """
        params = self._validate_request_data(request)
        endpoint = self.FIND_BY_DEVICE_NAME_ENDPOINT
        response = self._make_request(
            method=endpoint["method"],
            url=endpoint["url"],
            response_model=FindByDeviceNameResponse,
            params=params,
        )
        return response

    def find_by_conditions(self, request: FindByConditionsRequest) -> FindByConditionsResponse:
        """
        综合查询，支持多个字段模糊组合查询
        """
        params = self._validate_request_data(request)
        endpoint = self.FIND_BY_CONDITIONS_ENDPOINT
        response = self._make_request(
            method=endpoint["method"],
            url=endpoint["url"],
            response_model=FindByConditionsResponse,
            params=params,
        )
        return response

    # Async methods
    async def afind_by_sys_name(self, request: FindBySysNameRequest) -> FindBySysNameResponse:
        """
        根据系统名称模糊查询规则 (异步)
        """
        params = self._validate_request_data(request)
        endpoint = self.FIND_BY_SYS_NAME_ENDPOINT
        response = await self._amake_request(
            method=endpoint["method"],
            url=endpoint["url"],
            response_model=FindBySysNameResponse,
            params=params,
        )
        return response

    async def afind_by_sys_name_and_station_name(
        self, request: FindBySysNameAndStationNameRequest
    ) -> FindBySysNameAndStationNameResponse:
        """
        根据系统名称和站名称模糊查询 (异步)
        """
        params = self._validate_request_data(request)
        endpoint = self.FIND_BY_SYS_NAME_AND_STATION_NAME_ENDPOINT
        response = await self._amake_request(
            method=endpoint["method"],
            url=endpoint["url"],
            response_model=FindBySysNameAndStationNameResponse,
            params=params,
        )
        return response

    async def afind_exact_by_sys_name_and_station_name(
        self, request: FindExactBySysNameAndStationNameRequest
    ) -> FindExactBySysNameAndStationNameResponse:
        """
        根据系统名称和站名称精确查询 (异步)
        """
        params = self._validate_request_data(request)
        endpoint = self.FIND_EXACT_BY_SYS_NAME_AND_STATION_NAME_ENDPOINT
        response = await self._amake_request(
            method=endpoint["method"],
            url=endpoint["url"],
            response_model=FindExactBySysNameAndStationNameResponse,
            params=params,
        )
        return response

    async def afind_by_device_name(self, request: FindByDeviceNameRequest) -> FindByDeviceNameResponse:
        """
        根据设备名称模糊查询 (异步)
        """
        params = self._validate_request_data(request)
        endpoint = self.FIND_BY_DEVICE_NAME_ENDPOINT
        response = await self._amake_request(
            method=endpoint["method"],
            url=endpoint["url"],
            response_model=FindByDeviceNameResponse,
            params=params,
        )
        return response

    async def afind_by_conditions(self, request: FindByConditionsRequest) -> FindByConditionsResponse:
        """
        综合查询，支持多个字段模糊组合查询 (异步)
        """
        params = self._validate_request_data(request)
        endpoint = self.FIND_BY_CONDITIONS_ENDPOINT
        response = await self._amake_request(
            method=endpoint["method"],
            url=endpoint["url"],
            response_model=FindByConditionsResponse,
            params=params,
        )
        return response

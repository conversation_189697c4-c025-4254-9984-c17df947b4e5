"""
Unit template service for MeOS Python SDK.

This module provides unit template operations.
Tag: 单元模板信息
"""

import logging
from typing import Optional

from meos.services.common import CommonAttributeServiceMixin, CommonInstanceServiceMixin

from ..models.instances import (
    CommonBaseInfoListRequest,
    CommonBaseInfoListResponse,
    CommonDynamicInfoListRequest,
    CommonDynamicPropertyListResponse,
    CommonStaticInfoListRequest,
    CommonStaticPropertyListResponse,
    UnitInstanceResponse,
)

logger = logging.getLogger(__name__)


class UnitTemplateService(CommonAttributeServiceMixin, CommonInstanceServiceMixin):
    """Unit template service for MeOS API - 单元模板信息."""

    UNIT_MODEL_INFO_ENDPOINT = "/p/dt/v1/public/model/unit/info"
    BASE_PROPERTIES_ENDPOINT = "/p/dt/v1/public/model/unit/page/base"
    DYNAMIC_PROPERTIES_ENDPOINT = "/p/dt/v1/public/model/unit/page/dynamic"
    STATIC_PROPERTIES_ENDPOINT = "/p/dt/v1/public/model/unit/page/static"

    def get_unit_model_info(self, dataCode: Optional[str] = None) -> UnitInstanceResponse:
        """
        获取单元模型详细信息.
        Args:
            dataCode: 单元编码 (必填)
        Returns:
            完整的API响应, 包含 {code, msg, data} 结构
        Raises:
            MeOSAPIError: If the request fails
        """
        return self.get_instance_info(
            self.UNIT_MODEL_INFO_ENDPOINT, dataCode=dataCode, response_model=UnitInstanceResponse
        )

    async def get_unit_model_info_async(self, dataCode: Optional[str] = None) -> UnitInstanceResponse:
        """
        获取单元模型详细信息 (异步).
        Args:
            dataCode: 单元编码 (必填)
        Returns:
            完整的API响应, 包含 {code, msg, data} 结构
        Raises:
            MeOSAPIError: If the request fails
        """
        return await self.get_instance_info_async(
            self.UNIT_MODEL_INFO_ENDPOINT, dataCode=dataCode, response_model=UnitInstanceResponse
        )

    def get_base_properties(self, req: CommonBaseInfoListRequest) -> CommonBaseInfoListResponse:
        """
        获取基础属性列表.
        Args:
            req: CommonBaseInfoListRequest
        Returns:
            完整的API响应, 包含 {code, msg, data} 结构
        Raises:
            MeOSAPIError: If the request fails
        """
        return self.get_common_base_properties(self.BASE_PROPERTIES_ENDPOINT, req)

    async def get_base_properties_async(self, req: CommonBaseInfoListRequest) -> CommonBaseInfoListResponse:
        """
        获取基础属性列表 (异步).
        Args:
            req: CommonBaseInfoListRequest
        Returns:
            完整的API响应, 包含 {code, msg, data} 结构
        Raises:
            MeOSAPIError: If the request fails
        """
        return await self.get_common_base_properties_async(self.BASE_PROPERTIES_ENDPOINT, req)

    def get_dynamic_properties(self, req: CommonDynamicInfoListRequest) -> CommonDynamicPropertyListResponse:
        """
        获取动态属性列表.
        Args:
            req: CommonDynamicInfoListRequest
        Returns:
            完整的API响应, 包含 {code, msg, data} 结构
        Raises:
            MeOSAPIError: If the request fails
        """
        return self.get_common_dynamic_properties(self.DYNAMIC_PROPERTIES_ENDPOINT, req)

    async def get_dynamic_properties_async(
        self, req: CommonDynamicInfoListRequest
    ) -> CommonDynamicPropertyListResponse:
        """
        获取动态属性列表 (异步).
        Args:
            req: CommonDynamicInfoListRequest
        Returns:
            完整的API响应, 包含 {code, msg, data} 结构
        Raises:
            MeOSAPIError: If the request fails
        """
        return await self.get_common_dynamic_properties_async(self.DYNAMIC_PROPERTIES_ENDPOINT, req)

    def get_static_properties(self, req: CommonStaticInfoListRequest) -> CommonStaticPropertyListResponse:
        """
        获取静态属性列表.
        Args:
            req: CommonStaticInfoListRequest
        Returns:
            完整的API响应, 包含 {code, msg, data} 结构
        Raises:
            MeOSAPIError: If the request fails
        """
        return self.get_common_static_properties(self.STATIC_PROPERTIES_ENDPOINT, req)

    async def get_static_properties_async(self, req: CommonStaticInfoListRequest) -> CommonStaticPropertyListResponse:
        """
        获取静态属性列表 (异步).
        Args:
            req: CommonStaticInfoListRequest
        Returns:
            完整的API响应, 包含 {code, msg, data} 结构
        Raises:
            MeOSAPIError: If the request fails
        """
        return await self.get_common_static_properties_async(self.STATIC_PROPERTIES_ENDPOINT, req)

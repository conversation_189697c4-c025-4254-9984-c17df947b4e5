# MeOS Python SDK Documentation

Welcome to the MeOS Python SDK documentation.

## Overview

The MeOS Python SDK provides a comprehensive interface for interacting with the MeOS (Manufacturing Execution and Operations System) API. It offers both synchronous and asynchronous clients with full type safety and comprehensive error handling.

## Features

- **Full API Coverage**: Complete support for all MeOS API endpoints
- **Type Safety**: Full type hints and Pydantic models for all data structures
- **Async/Sync Support**: Both synchronous and asynchronous client implementations
- **Authentication**: Automatic token management and refresh
- **Error Handling**: Comprehensive exception hierarchy with detailed error information
- **Retry Logic**: Built-in retry mechanisms with exponential backoff
- **Logging**: Structured logging for debugging and monitoring

## Quick Start

```python
from meos import MeOSClient

# Initialize the client
client = MeOSClient(
    base_url="https://your-meos-instance.com",
    app_id="your_app_id",
    app_secret="your_app_secret"
)

# Get project information
project_info = client.instances.get_project_info("PROJECT_CODE")
print(f"Project: {project_info.pro_name}")

# Query dynamic data
data = client.data.get_dynamic_history(
    data_codes=["DATA_CODE_1", "DATA_CODE_2"],
    start_time="2024-01-01 00:00:00",
    end_time="2024-01-01 01:00:00"
)
```

## Installation

```bash
pip install meos-python-sdk
```

## Table of Contents

- [Authentication](auth.md)
- [Client Configuration](config.md)
- [API Reference](api.md)
- [Examples](examples.md)
- [Error Handling](errors.md)
- [Contributing](contributing.md)

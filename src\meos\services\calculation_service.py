"""
Calculation service for MeOS Python SDK.

This module provides calculation framework operations.
Tag: 计算框架-数据调用
"""

import logging

from ..models.common import (
    EquallySpacedTimeDataRequest,
    EquallySpacedTimeDataResponse,
    TimeIntervalDataRequest,
    TimeIntervalDataResponse,
    TimeSliceDataRequest,
    TimeSliceDataResponse,
)
from .base_service import BaseService

logger = logging.getLogger(__name__)


class CalculationService(BaseService):
    """Calculation service for MeOS API - 计算框架-数据调用."""

    GET_EQUALLY_SPACED_TIME_DATA_ENDPOINT = {"url": "/p/calc/api/history/getEquallySpacedTimeData", "method": "POST"}
    GET_TIME_INTERVAL_DATA_ENDPOINT = {"url": "/p/calc/api/history/getTimeIntervalData", "method": "POST"}
    GET_TIME_SLICE_DATA_ENDPOINT = {"url": "/p/calc/api/history/getTimeSliceData", "method": "POST"}

    def get_equally_spaced_time_data(
        self,
        request_data: EquallySpacedTimeDataRequest,
    ) -> EquallySpacedTimeDataResponse:
        """
        获取历史数据等时间间隔采样数据.
        """
        json_data = self._validate_request_data(request_data)
        endpoint = self.GET_EQUALLY_SPACED_TIME_DATA_ENDPOINT
        response = self._make_request(
            method=endpoint["method"],
            url=endpoint["url"],
            response_model=EquallySpacedTimeDataResponse,
            json_data=json_data,
        )
        return response

    async def get_equally_spaced_time_data_async(
        self,
        request_data: EquallySpacedTimeDataRequest,
    ) -> EquallySpacedTimeDataResponse:
        """
        获取历史数据等时间间隔采样数据 (异步).
        """
        json_data = self._validate_request_data(request_data)
        endpoint = self.GET_EQUALLY_SPACED_TIME_DATA_ENDPOINT
        response = await self._make_async_request(
            method=endpoint["method"],
            url=endpoint["url"],
            response_model=EquallySpacedTimeDataResponse,
            json_data=json_data,
        )
        return response

    def get_time_interval_data(
        self,
        request_data: TimeIntervalDataRequest,
    ) -> TimeIntervalDataResponse:
        """
        获取时间区间数据.
        """
        json_data = self._validate_request_data(request_data)
        endpoint = self.GET_TIME_INTERVAL_DATA_ENDPOINT
        response = self._make_request(
            method=endpoint["method"],
            url=endpoint["url"],
            response_model=TimeIntervalDataResponse,
            json_data=json_data,
        )
        return response

    async def get_time_interval_data_async(
        self,
        request_data: TimeIntervalDataRequest,
    ) -> TimeIntervalDataResponse:
        """
        获取时间区间数据 (异步).
        """
        json_data = self._validate_request_data(request_data)
        endpoint = self.GET_TIME_INTERVAL_DATA_ENDPOINT
        response = await self._make_async_request(
            method=endpoint["method"],
            url=endpoint["url"],
            response_model=TimeIntervalDataResponse,
            json_data=json_data,
        )
        return response

    def get_time_slice_data(
        self,
        request_data: TimeSliceDataRequest,
    ) -> TimeSliceDataResponse:
        """
        获取指定时间断面数据,最近15分钟最后一包数据.
        """
        json_data = self._validate_request_data(request_data)
        endpoint = self.GET_TIME_SLICE_DATA_ENDPOINT
        response = self._make_request(
            method=endpoint["method"],
            url=endpoint["url"],
            response_model=TimeSliceDataResponse,
            json_data=json_data,
        )
        return response

    async def get_time_slice_data_async(
        self,
        request_data: TimeSliceDataRequest,
    ) -> TimeSliceDataResponse:
        """
        获取指定时间断面数据,最近15分钟最后一包数据 (异步).
        """
        json_data = self._validate_request_data(request_data)
        endpoint = self.GET_TIME_SLICE_DATA_ENDPOINT
        response = await self._make_async_request(
            method=endpoint["method"],
            url=endpoint["url"],
            response_model=TimeSliceDataResponse,
            json_data=json_data,
        )
        return response

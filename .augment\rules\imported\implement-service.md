---
type: "agent_requested"
---

## 📌 接口仿写实现任务

请根据指定文件中的内容规范和代码模式，对新功能或接口进行仿照/新增实现。

### 在开始实现前，请先完成以下步骤：

1. **接口归类与调研**：
   - 根据当前文件内容，明确目标接口所属的模块或类别。
   - 查询并查阅 **Meos MCP 接口文档**，重点了解以下内容：
     - 接口的请求地址（Endpoint）
     - 请求参数（入参）
     - 返回参数（返参）
     - 请求方式（GET / POST / PUT / DELETE 等）
     - 异常处理与错误码定义

2. **已有基础组件使用分析**:
   - 完全理解当前已有的基础组件, 合理使用这些基础功能进行业务组合
     - src\meos\services\common\ 目录下的文件, 定义了获取信息的通用接口、base/dynamic/static的通用接口
     - src\meos\services\base_service.py 中定义了基于http请求组件的抽象接口
     - src\meos\base.py 中定义了请求http的基础组件

3. **已有代码复用判断**：
   - 查找是否存在功能或结构相似的现有接口实现。
   - 若已有抽象或通用代码可供复用，请优先引用，避免重复开发。
   - 若无相似实现，则基于已有代码风格，进行新增接口开发。

4. **输出实现方案确认**：
   - 在编写具体代码前，请先输出接口实现的思路和结构设计，以及相应的新模块所有的接口信息和openapi路径/方法/参数说明等。
   - 待我确认无误后，再继续进行代码编写。

5. **代码规范要求**：
   - 整个实现过程必须严格遵循我当前项目的代码风格、命名规范、注释要求(使用英文标点符号)等。
   - 保持代码简洁、可维护，并具备良好的可读性。


### 反馈与自我修正

没当完成一个service的代码实现后, 重新请求meos mcp服务对当前的实现 - 请求/响应定义、接口复用, 进行review; 如果有错误, 则重新修复, 直到实现完全正确, 方可退出任务.

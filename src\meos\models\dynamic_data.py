"""
Dynamic data models for MeOS Python SDK.

This module contains models for dynamic data operations.
"""

from datetime import datetime
from typing import List, Union

from pydantic import Field, model_validator
from typing_extensions import Self

from .base import BaseModel, BaseResponse


# --- 历史数据 ---
class DynamicHistoryVal(BaseModel):
    propVal: str = Field(..., description="属性值")
    updateTime: str = Field(..., description="更新时间")


class DynamicHistoryItem(BaseModel):
    dataCode: str = Field(..., description="属性编码")
    vals: List[DynamicHistoryVal] = Field(..., description="属性历史数据")


class DynamicHistoryResponse(BaseResponse[List[DynamicHistoryItem]]):
    pass


# --- 实时数据 ---
class DynamicRealtimeItem(BaseModel):
    dataCode: str = Field(..., description="属性编码")
    propName: str = Field(..., description="属性名称")
    propVal: str = Field(..., description="属性值")
    updateTime: str = Field(..., description="更新时间")


class DynamicRealtimeResponse(BaseResponse[List[DynamicRealtimeItem]]):
    pass


# --- 请求类 ---
class DynamicHistoryRequest(BaseModel):
    dataCodes: List[str] = Field(..., description="属性编码")
    startTime: Union[str, datetime] = Field(..., description="开始时间")
    endTime: Union[str, datetime] = Field(..., description="结束时间")

    @model_validator(mode="after")
    def validate_time_range(self) -> Self:
        _end_time, _start_time = (
            datetime.strptime(self.endTime, "%Y-%m-%d %H:%M:%S"),
            datetime.strptime(self.startTime, "%Y-%m-%d %H:%M:%S"),
        )

        if _end_time <= _start_time:
            raise ValueError("endTime must be after startTime")
        return self


class DynamicRealtimeRequest(BaseModel):
    dataCodes: List[str] = Field(..., description="属性编码")


class DynamicRealtimeWithFormulaRequest(BaseModel):
    """Dynamic property realtime data with formula calculation request model."""

    dataCodes: List[str] = Field(..., description="属性编码列表")


# Response models
class DynamicRealtimeWithFormulaResponse(BaseResponse[List[DynamicRealtimeItem]]):
    """Dynamic property realtime with formula response model."""

    pass

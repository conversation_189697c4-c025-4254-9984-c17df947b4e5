"""
Route service for MeOS Python SDK.

This module provides route management operations.
"""

import logging

from ..models.common import GetRouteResponse, RouteRequest, RouteResponse, SimpleResponse
from .base_service import BaseService

logger = logging.getLogger(__name__)


class RouteService(BaseService):
    """Route service for MeOS API - 路由管理."""

    CREATE_ROUTE_ENDPOINT = {"url": "/p/sbus/admin/routes", "method": "POST"}
    UPDATE_ROUTE_ENDPOINT = {"url": "/p/sbus/admin/routes/{route_id}", "method": "PUT"}
    GET_ROUTE_ENDPOINT = {"url": "/p/sbus/admin/routes/{route_id}", "method": "GET"}
    UPDATE_ROUTE_STATUS_ENDPOINT = {"url": "/p/sbus/admin/routes/{route_id}", "method": "PATCH"}
    DELETE_ROUTE_ENDPOINT = {"url": "/p/sbus/admin/routes/{route_id}", "method": "DELETE"}

    def create_route(
        self,
        request: RouteRequest,
    ) -> RouteResponse:
        """
        注册路由.
        """
        json_data = self._validate_request_data(request)
        endpoint = self.CREATE_ROUTE_ENDPOINT
        response = self._make_request(
            method=endpoint["method"],
            url=endpoint["url"],
            response_model=RouteResponse,
            json_data=json_data,
        )
        return response

    def update_route(
        self,
        route_id: str,
        request: RouteRequest,
    ) -> RouteResponse:
        """
        修改路由.
        """
        json_data = self._validate_request_data(request)
        endpoint = self.UPDATE_ROUTE_ENDPOINT
        response = self._make_request(
            method=endpoint["method"],
            url=endpoint["url"].format(route_id=route_id),
            response_model=RouteResponse,
            json_data=json_data,
        )
        return response

    def get_route(self, route_id: str) -> GetRouteResponse:
        """
        查询路由.
        """
        endpoint = self.GET_ROUTE_ENDPOINT
        response = self._make_request(
            method=endpoint["method"],
            url=endpoint["url"].format(route_id=route_id),
            response_model=GetRouteResponse,
        )
        return response

    def update_route_status(self, route_id: str, status: int) -> SimpleResponse:
        """
        路由上/下线.
        """
        json_data = self._validate_request_data({"status": status})
        endpoint = self.UPDATE_ROUTE_STATUS_ENDPOINT
        response = self._make_request(
            method=endpoint["method"],
            url=endpoint["url"].format(route_id=route_id),
            response_model=SimpleResponse,
            json_data=json_data,
        )
        return response

    def delete_route(self, route_id: str) -> SimpleResponse:
        """
        删除路由.
        """
        endpoint = self.DELETE_ROUTE_ENDPOINT
        response = self._make_request(
            method=endpoint["method"],
            url=endpoint["url"].format(route_id=route_id),
            response_model=SimpleResponse,
        )
        return response

    # Async methods
    async def acreate_route(
        self,
        request: RouteRequest,
    ) -> RouteResponse:
        """
        注册路由 (异步).
        """
        json_data = self._validate_request_data(request)
        endpoint = self.CREATE_ROUTE_ENDPOINT
        response = await self._make_async_request(
            method=endpoint["method"],
            url=endpoint["url"],
            response_model=RouteResponse,
            json_data=json_data,
        )
        return response

    async def aupdate_route(
        self,
        route_id: str,
        request: RouteRequest,
    ) -> RouteResponse:
        """
        修改路由 (异步).
        """
        json_data = self._validate_request_data(request)
        endpoint = self.UPDATE_ROUTE_ENDPOINT
        response = await self._make_async_request(
            method=endpoint["method"],
            url=endpoint["url"].format(route_id=route_id),
            response_model=RouteResponse,
            json_data=json_data,
        )
        return response

    async def aget_route(self, route_id: str) -> GetRouteResponse:
        """
        查询路由 (异步).
        """
        endpoint = self.GET_ROUTE_ENDPOINT
        response = await self._make_async_request(
            method=endpoint["method"],
            url=endpoint["url"].format(route_id=route_id),
            response_model=GetRouteResponse,
        )
        return response

    async def aupdate_route_status(self, route_id: str, status: int) -> SimpleResponse:
        """
        路由上/下线 (异步).
        """
        json_data = self._validate_request_data({"status": status})
        endpoint = self.UPDATE_ROUTE_STATUS_ENDPOINT
        response = await self._make_async_request(
            method=endpoint["method"],
            url=endpoint["url"].format(route_id=route_id),
            response_model=SimpleResponse,
            json_data=json_data,
        )
        return response

    async def adelete_route(self, route_id: str) -> SimpleResponse:
        """
        删除路由 (异步).
        """
        endpoint = self.DELETE_ROUTE_ENDPOINT
        response = await self._make_async_request(
            method=endpoint["method"],
            url=endpoint["url"].format(route_id=route_id),
            response_model=SimpleResponse,
        )
        return response

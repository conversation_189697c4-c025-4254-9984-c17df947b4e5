"""
Configuration management for MeOS Python SDK.

This module provides configuration classes and utilities.
"""

from __future__ import annotations

import logging
from dataclasses import dataclass, field
from typing import Any, Dict, Union

from .exceptions import MeOSConfigurationError

logger = logging.getLogger(__name__)


@dataclass
class RetryConfig:
    """Configuration for request retry behavior."""

    max_retries: int = 3
    backoff_factor: float = 0.5
    backoff_max: float = 60.0
    retry_on_status: tuple = (429, 500, 502, 503, 504)
    retry_on_connection_error: bool = True

    def __post_init__(self) -> None:
        """Validate retry configuration."""
        if self.max_retries < 0:
            raise MeOSConfigurationError("max_retries must be non-negative", parameter="max_retries")

        if self.backoff_factor < 0:
            raise MeOSConfigurationError("backoff_factor must be non-negative", parameter="backoff_factor")

        if self.backoff_max <= 0:
            raise MeOSConfigurationError("backoff_max must be positive", parameter="backoff_max")


@dataclass
class TimeoutConfig:
    """Configuration for request timeouts."""

    connect: float = 10.0
    read: float = 30.0
    write: float = 10.0
    pool: float = 10.0

    def __post_init__(self) -> None:
        """Validate timeout configuration."""
        for name, value in [
            ("connect", self.connect),
            ("read", self.read),
            ("write", self.write),
            ("pool", self.pool),
        ]:
            if value <= 0:
                raise MeOSConfigurationError(f"{name} timeout must be positive", parameter=name)

    @property
    def total(self) -> float:
        """Get total timeout (connect + read)."""
        return self.connect + self.read

    def to_httpx_timeout(self) -> Dict[str, float]:
        """Convert to httpx timeout format."""
        return {
            "connect": self.connect,
            "read": self.read,
            "write": self.write,
            "pool": self.pool,
        }


@dataclass
class LoggingConfig:
    """Configuration for SDK logging."""

    level: Union[int, str] = logging.INFO
    format: str = "%(asctime)s - %(levelname)s - %(filename)s:%(lineno)d - %(message)s"
    log_requests: bool = False
    log_responses: bool = False
    log_request_bodies: bool = False
    log_response_bodies: bool = False

    def __post_init__(self) -> None:
        """Validate logging configuration."""
        if isinstance(self.level, str):
            try:
                self.level = getattr(logging, self.level.upper())
            except AttributeError:
                raise MeOSConfigurationError(f"Invalid logging level: {self.level}", parameter="level")


@dataclass
class ClientConfig:
    """Main configuration class for MeOS SDK clients."""

    base_url: str

    # Authentication
    app_id: str
    app_secret: str

    # Optional configurations
    timeout: TimeoutConfig = field(default_factory=TimeoutConfig)
    retry: RetryConfig = field(default_factory=RetryConfig)
    logging: LoggingConfig = field(default_factory=LoggingConfig)

    # HTTP client options
    verify_ssl: bool = True
    follow_redirects: bool = True
    max_connections: int = 100
    max_keepalive_connections: int = 20
    keepalive_expiry: float = 5.0

    # API specific options
    token_endpoint: str = "/p/token"
    token_lifetime: int = 7200  # 2 hours
    token_refresh_threshold: int = 300  # 5 minutes

    # Additional headers
    extra_headers: Dict[str, str] = field(default_factory=dict)

    def __post_init__(self) -> None:
        """Validate client configuration."""
        # Validate required fields
        if not self.app_id:
            raise MeOSConfigurationError("app_id is required", parameter="app_id")

        if not self.app_secret:
            raise MeOSConfigurationError("app_secret is required", parameter="app_secret")

        if not self.base_url:
            raise MeOSConfigurationError("base_url is required", parameter="base_url")

        # Normalize base URL
        self.base_url = self.base_url.rstrip("/")

        # Validate numeric fields
        if self.max_connections <= 0:
            raise MeOSConfigurationError("max_connections must be positive", parameter="max_connections")

        if self.max_keepalive_connections <= 0:
            raise MeOSConfigurationError(
                "max_keepalive_connections must be positive", parameter="max_keepalive_connections"
            )

        if self.keepalive_expiry <= 0:
            raise MeOSConfigurationError("keepalive_expiry must be positive", parameter="keepalive_expiry")

        if self.token_lifetime <= 0:
            raise MeOSConfigurationError("token_lifetime must be positive", parameter="token_lifetime")

        if self.token_refresh_threshold < 0:
            raise MeOSConfigurationError(
                "token_refresh_threshold must be non-negative", parameter="token_refresh_threshold"
            )

        if self.token_refresh_threshold >= self.token_lifetime:
            raise MeOSConfigurationError(
                "token_refresh_threshold must be less than token_lifetime", parameter="token_refresh_threshold"
            )

    @classmethod
    def from_dict(cls, config_dict: Dict[str, Any]) -> ClientConfig:
        """
        Create ClientConfig from a dictionary.

        Args:
            config_dict: Configuration dictionary

        Returns:
            ClientConfig instance
        """
        # Extract nested configurations
        timeout_config = config_dict.pop("timeout", {})
        retry_config = config_dict.pop("retry", {})
        logging_config = config_dict.pop("logging", {})

        return cls(
            timeout=TimeoutConfig(**timeout_config),
            retry=RetryConfig(**retry_config),
            logging=LoggingConfig(**logging_config),
            **config_dict,
        )

    def to_dict(self) -> Dict[str, Any]:
        """
        Convert ClientConfig to a dictionary.

        Returns:
            Configuration dictionary
        """
        return {
            "app_id": self.app_id,
            "app_secret": self.app_secret,
            "base_url": self.base_url,
            "timeout": {
                "connect": self.timeout.connect,
                "read": self.timeout.read,
                "write": self.timeout.write,
                "pool": self.timeout.pool,
            },
            "retry": {
                "max_retries": self.retry.max_retries,
                "backoff_factor": self.retry.backoff_factor,
                "backoff_max": self.retry.backoff_max,
                "retry_on_status": self.retry.retry_on_status,
                "retry_on_connection_error": self.retry.retry_on_connection_error,
            },
            "logging": {
                "level": self.logging.level,
                "format": self.logging.format,
                "log_requests": self.logging.log_requests,
                "log_responses": self.logging.log_responses,
                "log_request_bodies": self.logging.log_request_bodies,
                "log_response_bodies": self.logging.log_response_bodies,
            },
            "verify_ssl": self.verify_ssl,
            "follow_redirects": self.follow_redirects,
            "max_connections": self.max_connections,
            "max_keepalive_connections": self.max_keepalive_connections,
            "keepalive_expiry": self.keepalive_expiry,
            "token_endpoint": self.token_endpoint,
            "token_lifetime": self.token_lifetime,
            "token_refresh_threshold": self.token_refresh_threshold,
            "extra_headers": self.extra_headers.copy(),
        }

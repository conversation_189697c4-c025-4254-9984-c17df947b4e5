"""
Topology service for MeOS Python SDK.

This module provides topology relationship operations.
Tag: 拓扑关系
"""

import logging

from ..models.instances import GetTopologyResponse
from .base_service import BaseService

logger = logging.getLogger(__name__)


class TopologyService(BaseService):
    """Topology service for MeOS API - 拓扑关系。"""

    GET_TOPOLOGY_ENDPOINT = {"url": "/p/dt/v1/ins/topotaxy", "method": "GET"}

    def get_topology(self, topotaxyId: str) -> GetTopologyResponse:
        """
        拓扑关系 (summary: 拓扑关系)。

        Args:
            topotaxyId: 拓扑关系请求模型拓扑Id

        Returns:
            GetTopologyResponse 拓扑关系响应模型

        Raises:
            MeOSAPIError: If the request fails
        """
        params = self._build_query_params(topotaxyId=topotaxyId)

        response = self._make_request(
            method=self.GET_TOPOLOGY_ENDPOINT["method"],
            url=self.GET_TOPOLOGY_ENDPOINT["url"],
            response_model=GetTopologyResponse,
            params=params,
        )

        return response

    async def get_topology_async(self, topotaxyId: str) -> GetTopologyResponse:
        """
        拓扑关系 (异步, summary: 拓扑关系)。

        Args:
            topotaxyId: 拓扑关系请求模型拓扑Id

        Returns:
            GetTopologyResponse 拓扑关系响应模型

        Raises:
            MeOSAPIError: If the request fails
        """
        params = self._build_query_params(topotaxyId=topotaxyId)

        response = await self._make_async_request(
            method=self.GET_TOPOLOGY_ENDPOINT["method"],
            url=self.GET_TOPOLOGY_ENDPOINT["url"],
            response_model=GetTopologyResponse,
            params=params,
        )
        return response

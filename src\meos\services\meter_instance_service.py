"""Meter instance service placeholder.

This module provides Meter instance operations.
Tag: 表计实例信息
"""

import logging
from typing import Optional

from meos.services.common import CommonAttributeServiceMixin, CommonInstanceServiceMixin

from ..models.instances import (
    CommonBaseInfoListRequest,
    CommonBaseInfoListResponse,
    CommonDynamicInfoListRequest,
    CommonDynamicPropertyListResponse,
    CommonStaticInfoListRequest,
    CommonStaticPropertyListResponse,
    MeterInstanceResponse,
    MeterInstanceTreeResponse,
)

logger = logging.getLogger(__name__)


class MeterInstanceService(CommonAttributeServiceMixin, CommonInstanceServiceMixin):
    """Meter instance service for MeOS API - 表计实例信息。"""

    METER_INFO_ENDPOINT = "/p/dt/v1/ins/meter/info"
    BASE_PROPERTIES_ENDPOINT = "/p/dt/v1/ins/meter/page/base"
    DYNAMIC_PROPERTIES_ENDPOINT = "/p/dt/v1/ins/meter/page/dynamic"
    STATIC_PROPERTIES_ENDPOINT = "/p/dt/v1/ins/meter/page/static"
    METER_TREE_ENDPOINT = {"url": "/p/dt/v1/ins/meter/ins/meter/tree", "method": "GET"}

    def get_meter_info(self, dataCode: Optional[str] = None) -> MeterInstanceResponse:
        """
        获取表计实例详细信息.

        Args:
            dataCode: 表计数据编码

        Returns:
            完整的API响应, 包含 {code, msg, data} 结构

        Raises:
            MeOSAPIError: If the request fails
        """

        return self.get_instance_info(self.METER_INFO_ENDPOINT, dataCode=dataCode, response_model=MeterInstanceResponse)

    async def get_meter_info_async(self, dataCode: Optional[str] = None) -> MeterInstanceResponse:
        """
        获取表计实例详细信息 (异步).

        Args:
            dataCode: 表计数据编码

        Returns:
            完整的API响应, 包含 {code, msg, data} 结构

        Raises:
            MeOSAPIError: If the request fails
        """

        return await self.get_instance_info_async(
            self.METER_INFO_ENDPOINT, dataCode=dataCode, response_model=MeterInstanceResponse
        )

    def get_base_properties(
        self,
        req: CommonBaseInfoListRequest,
    ) -> CommonBaseInfoListResponse:
        """
        获取基础属性列表.

        Args:
            req: CommonBaseInfoListRequest
        Returns:
            完整的API响应, 包含 {code, msg, data} 结构
        Raises:
            MeOSAPIError: If the request fails
        """

        return self.get_common_base_properties(self.BASE_PROPERTIES_ENDPOINT, req)

    async def get_base_properties_async(
        self,
        req: CommonBaseInfoListRequest,
    ) -> CommonBaseInfoListResponse:
        """
        获取基础属性列表 (异步).

        Args:
            req: CommonBaseInfoListRequest

        Returns:
            完整的API响应, 包含 {code, msg, data} 结构

        Raises:
            MeOSAPIError: If the request fails
        """

        return await self.get_common_base_properties_async(self.BASE_PROPERTIES_ENDPOINT, req)

    def get_dynamic_properties(
        self,
        req: CommonDynamicInfoListRequest,
    ) -> CommonDynamicPropertyListResponse:
        """
        获取动态属性列表.

        Args:
            req: CommonDynamicInfoListRequest
        Returns:
            完整的API响应, 包含 {code, msg, data} 结构
        Raises:
            MeOSAPIError: If the request fails
        """

        return self.get_common_dynamic_properties(self.DYNAMIC_PROPERTIES_ENDPOINT, req)

    async def get_dynamic_properties_async(
        self,
        req: CommonDynamicInfoListRequest,
    ) -> CommonDynamicPropertyListResponse:
        """
        获取动态属性列表 (异步).

        Args:
            req: CommonDynamicInfoListRequest

        Returns:
            完整的API响应, 包含 {code, msg, data} 结构

        Raises:
            MeOSAPIError: If the request fails
        """

        return await self.get_common_dynamic_properties_async(self.DYNAMIC_PROPERTIES_ENDPOINT, req)

    def get_static_properties(
        self,
        req: CommonStaticInfoListRequest,
    ) -> CommonStaticPropertyListResponse:
        """
        获取静态属性列表.

        Args:
            req: CommonStaticInfoListRequest
        Returns:
            完整的API响应, 包含 {code, msg, data} 结构
        Raises:
            MeOSAPIError: If the request fails
        """

        return self.get_common_static_properties(self.STATIC_PROPERTIES_ENDPOINT, req)

    async def get_static_properties_async(
        self,
        req: CommonStaticInfoListRequest,
    ) -> CommonStaticPropertyListResponse:
        """
        获取静态属性列表 (异步).

        Args:
            req: CommonStaticInfoListRequest

        Returns:
            完整的API响应, 包含 {code, msg, data} 结构

        Raises:
            MeOSAPIError: If the request fails
        """

        return await self.get_common_static_properties_async(self.STATIC_PROPERTIES_ENDPOINT, req)

    def get_meter_tree(self, dataCode: str) -> MeterInstanceTreeResponse:
        """
        查询表计实例所有下级 (树) .

        Args:
            dataCode: 项目数据编码
        Returns:
            完整的API响应, 包含 {code, msg, data} 结构
        Raises:
            MeOSAPIError: If the request fails
        """

        endpoint = self.METER_TREE_ENDPOINT
        params = self._build_query_params(dataCode=dataCode)
        response = self._make_request(
            method=endpoint["method"],
            url=endpoint["url"],
            response_model=MeterInstanceTreeResponse,
            params=params,
        )
        return response

    async def get_meter_tree_async(self, data_code: str) -> MeterInstanceTreeResponse:
        """
        查询表计实例所有下级 (树) (异步).

        Args:
            data_code: 项目数据编码

        Returns:
            完整的API响应, 包含 {code, msg, data} 结构

        Raises:
            MeOSAPIError: If the request fails
        """

        endpoint = self.METER_TREE_ENDPOINT
        params = self._build_query_params(dataCode=data_code)
        response = await self._make_async_request(
            method=endpoint["method"],
            url=endpoint["url"],
            response_model=MeterInstanceTreeResponse,
            params=params,
        )
        return response

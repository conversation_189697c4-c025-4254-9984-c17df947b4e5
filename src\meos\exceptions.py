"""
Exception classes for MeOS Python SDK.

This module defines the exception hierarchy used throughout the SDK.
"""

from typing import Any, Dict, Optional


class MeOSError(Exception):
    """Base exception class for all MeOS SDK errors."""

    def __init__(
        self,
        message: str,
        code: Optional[int] = None,
        details: Optional[Dict[str, Any]] = None,
    ) -> None:
        super().__init__(message)
        self.message = message
        self.code = code
        self.details = details or {}

    def __str__(self) -> str:
        if self.code:
            return f"[{self.code}] {self.message}"
        return self.message

    def __repr__(self) -> str:
        return f"{self.__class__.__name__}(message={self.message!r}, code={self.code})"


class MeOSAPIError(MeOSError):
    """Exception raised when the MeOS API returns an error response."""

    def __init__(
        self,
        message: str,
        code: Optional[int] = None,
        status_code: Optional[int] = None,
        response_data: Optional[Dict[str, Any]] = None,
    ) -> None:
        super().__init__(message, code, response_data)
        self.status_code = status_code
        self.response_data = response_data or {}


class MeOSAuthenticationError(MeOSAPIError):
    """Exception raised when authentication fails."""

    def __init__(
        self,
        message: str = "Authentication failed",
        code: Optional[int] = None,
        status_code: Optional[int] = None,
        response_data: Optional[Dict[str, Any]] = None,
    ) -> None:
        super().__init__(message, code, status_code, response_data)


class MeOSAuthorizationError(MeOSAPIError):
    """Exception raised when authorization fails (403 Forbidden)."""

    def __init__(
        self,
        message: str = "Access forbidden",
        code: Optional[int] = None,
        status_code: Optional[int] = None,
        response_data: Optional[Dict[str, Any]] = None,
    ) -> None:
        super().__init__(message, code, status_code, response_data)


class MeOSNotFoundError(MeOSAPIError):
    """Exception raised when a resource is not found (404)."""

    def __init__(
        self,
        message: str = "Resource not found",
        code: Optional[int] = None,
        status_code: Optional[int] = None,
        response_data: Optional[Dict[str, Any]] = None,
    ) -> None:
        super().__init__(message, code, status_code, response_data)


class MeOSValidationError(MeOSError):
    """Exception raised when request validation fails."""

    def __init__(
        self,
        message: str,
        field: Optional[str] = None,
        value: Optional[Any] = None,
    ) -> None:
        super().__init__(message)
        self.field = field
        self.value = value


class MeOSConnectionError(MeOSError):
    """Exception raised when connection to MeOS API fails."""

    def __init__(
        self,
        message: str = "Connection failed",
        original_error: Optional[Exception] = None,
    ) -> None:
        super().__init__(message)
        self.original_error = original_error


class MeOSTimeoutError(MeOSError):
    """Exception raised when a request times out."""

    def __init__(
        self,
        message: str = "Request timed out",
        timeout: Optional[float] = None,
    ) -> None:
        super().__init__(message)
        self.timeout = timeout


class MeOSRateLimitError(MeOSAPIError):
    """Exception raised when rate limit is exceeded."""

    def __init__(
        self,
        message: str = "Rate limit exceeded",
        retry_after: Optional[int] = None,
        code: Optional[int] = None,
        status_code: Optional[int] = None,
        response_data: Optional[Dict[str, Any]] = None,
    ) -> None:
        super().__init__(message, code, status_code, response_data)
        self.retry_after = retry_after


class MeOSServerError(MeOSAPIError):
    """Exception raised when the server returns a 5xx error."""

    def __init__(
        self,
        message: str = "Server error",
        code: Optional[int] = None,
        status_code: Optional[int] = None,
        response_data: Optional[Dict[str, Any]] = None,
    ) -> None:
        super().__init__(message, code, status_code, response_data)


class MeOSConfigurationError(MeOSError):
    """Exception raised when SDK configuration is invalid."""

    def __init__(
        self,
        message: str,
        parameter: Optional[str] = None,
    ) -> None:
        super().__init__(message)
        self.parameter = parameter


def create_api_error(
    status_code: int,
    response_data: Optional[Dict[str, Any]] = None,
    message: Optional[str] = None,
) -> MeOSAPIError:
    """
    Create an appropriate API error based on the HTTP status code.

    Args:
        status_code: HTTP status code
        response_data: Response data from the API
        message: Custom error message

    Returns:
        Appropriate MeOSAPIError subclass instance
    """
    response_data = response_data or {}

    # Extract message and code from response if available
    api_message = response_data.get("msg", "")
    api_code = response_data.get("code")

    # Use provided message or fall back to API message
    error_message = message or api_message or f"HTTP {status_code} error"

    if status_code == 401:
        return MeOSAuthenticationError(
            message=error_message,
            code=api_code,
            status_code=status_code,
            response_data=response_data,
        )
    elif status_code == 403:
        return MeOSAuthorizationError(
            message=error_message,
            code=api_code,
            status_code=status_code,
            response_data=response_data,
        )
    elif status_code == 404:
        return MeOSNotFoundError(
            message=error_message,
            code=api_code,
            status_code=status_code,
            response_data=response_data,
        )
    elif status_code == 429:
        retry_after = None
        if "Retry-After" in response_data:
            try:
                retry_after = int(response_data["Retry-After"])
            except (ValueError, TypeError):
                pass

        return MeOSRateLimitError(
            message=error_message,
            retry_after=retry_after,
            code=api_code,
            status_code=status_code,
            response_data=response_data,
        )
    elif 500 <= status_code < 600:
        return MeOSServerError(
            message=error_message,
            code=api_code,
            status_code=status_code,
            response_data=response_data,
        )
    else:
        return MeOSAPIError(
            message=error_message,
            code=api_code,
            status_code=status_code,
            response_data=response_data,
        )

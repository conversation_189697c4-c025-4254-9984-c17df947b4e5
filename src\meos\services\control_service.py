"""
Control service for MeOS Python SDK.

This module provides control operations.
Tag: 控制接口
"""

import logging

from ..models.common import ControlCommandDetail, ControlCommandResponse, FeedbackValueResponse
from .base_service import BaseService

logger = logging.getLogger(__name__)


class ControlService(BaseService):
    """Control service for MeOS API - 控制接口。"""

    ISSUE_CONTROL_COMMAND_ENDPOINT = {"url": "/p/control/issue", "method": "POST"}
    QUERY_FEEDBACK_VALUE_ENDPOINT = {"url": "/p/control/queryFeedbackVal", "method": "POST"}

    def issue_control_command(
        self,
        command: ControlCommandDetail,
    ) -> ControlCommandResponse:
        """
        下发控制命令.

        Args:
            command: 控制指令详情, ControlCommandDetail 实例, 字段严格匹配OpenAPI

        Returns:
            ControlCommandResponse, 包含 code, msg, data

        Raises:
            MeOSAPIError: If the request fails
        """
        endpoint = self.ISSUE_CONTROL_COMMAND_ENDPOINT
        request_data = self._validate_request_data(command)
        response = self._make_request(
            method=endpoint["method"],
            url=endpoint["url"],
            response_model=ControlCommandResponse,
            json_data=request_data,
        )
        return response

    async def issue_control_command_async(
        self,
        command: ControlCommandDetail,
    ) -> ControlCommandResponse:
        """
        下发控制命令 (异步).

        Args:
            command: 控制指令详情, ControlCommandDetail 实例, 字段严格匹配OpenAPI

        Returns:
            ControlCommandResponse, 包含 code, msg, data

        Raises:
            MeOSAPIError: If the request fails
        """
        endpoint = self.ISSUE_CONTROL_COMMAND_ENDPOINT
        request_data = self._validate_request_data(command)
        response = await self._make_async_request(
            method=endpoint["method"],
            url=endpoint["url"],
            response_model=ControlCommandResponse,
            json_data=request_data,
        )
        return response

    def query_feedback_value(
        self,
        data_codes: list[str],
    ) -> FeedbackValueResponse:
        """
        数据实时值查询.

        Args:
            data_codes: 查询属性编码列表, list[str]

        Returns:
            FeedbackValueResponse, 包含 code, msg, data(List[FeedbackValueItem])

        Raises:
            MeOSAPIError: If the request fails
        """
        endpoint = self.QUERY_FEEDBACK_VALUE_ENDPOINT
        response = self._make_request(
            method=endpoint["method"],
            url=endpoint["url"],
            response_model=FeedbackValueResponse,
            json_data=data_codes,
        )
        return response

    async def query_feedback_value_async(
        self,
        data_codes: list[str],
    ) -> FeedbackValueResponse:
        """
        数据实时值查询 (异步).

        Args:
            data_codes: 查询属性编码列表, list[str]

        Returns:
            FeedbackValueResponse, 包含 code, msg, data(List[FeedbackValueItem])

        Raises:
            MeOSAPIError: If the request fails
        """
        endpoint = self.QUERY_FEEDBACK_VALUE_ENDPOINT
        response = await self._make_async_request(
            method=endpoint["method"],
            url=endpoint["url"],
            response_model=FeedbackValueResponse,
            json_data=data_codes,
        )
        return response

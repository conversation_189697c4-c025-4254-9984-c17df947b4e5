---
type: "agent_requested"
---

# 🛠️ OpenAPI 驱动的 Python SDK 实现经验总结

## 1. 需求分析与接口调研

- **明确业务边界**：首先要清楚目标接口属于哪个业务域（如“管路实例信息”），并确定其在整体系统中的定位。
- **查阅 OpenAPI 文档**：优先获取接口的 OpenAPI 规范，关注接口路径、请求方法、参数、响应结构、字段类型与描述。
- **接口归类**：将接口分为“单体详情”、“列表/分页”、“树结构”、“属性查询”等常见类型，便于后续代码复用。

---

## 2. Pydantic 数据模型建模

- **字段对齐**：严格按照 OpenAPI 返回结构定义 Pydantic 模型，字段名、类型、描述（description）均需一致。
- **冗余字段处理**：如发现模型中有 OpenAPI 未定义的字段，应及时剔除；如有缺失字段则需补充。
- **继承与复用**：对于通用字段（如 dataCode、descr、reserve1~5），建议抽象为基类（如 BaseInstanceInfo），子模型继承。
- **注释规范**：每个字段都应有明确的 description，便于 IDE 智能提示和自动文档生成。

---

## 3. Service 层接口实现

- **接口风格统一**：所有服务类方法应支持同步和异步两种调用方式，命名风格统一（如 get_xxx_info / get_xxx_info_async）。
- **参数与返回类型**：方法参数、返回类型、异常处理均需类型注解，且与 OpenAPI 保持一致。
- **通用 Mixin 复用**：如有通用的 Mixin（如 CommonAttributeServiceMixin, CommonInstanceServiceMixin），优先复用，减少重复代码。
- **注释与文档**：每个方法需有详细 docstring，说明用途、参数、返回值、异常等。

---

## 4. 代码风格与一致性

- **命名规范**：类名、方法名、参数名、字段名等均应遵循项目统一风格（如 camelCase、snake_case）。
- **类型安全**：充分利用 Python 类型注解和 Pydantic 校验，提升代码健壮性和开发体验。
- **异常处理**：所有接口方法应对 API 错误进行统一捕获和抛出自定义异常（如 MeOSAPIError）。

---

## 5. 迭代与维护建议

- **接口变更同步**：如 OpenAPI 文档有更新，需及时同步模型和服务层代码，保持一致。
- **自动化测试**：建议为每个接口编写单元测试和集成测试，确保 SDK 行为与接口文档一致。
- **文档自动生成**：利用 Pydantic 和 docstring，可自动生成 SDK 使用文档，提升可维护性。
- **经验复用**：本轮实现思路可直接迁移到其他业务域（如设备、站点、单元等），实现高效扩展。

---

## 6. 常见问题与解决方案

- **字段遗漏/冗余**：始终以 OpenAPI 文档为准，定期比对模型与接口返回结构。
- **异步支持**：建议所有接口均实现同步和异步版本，满足不同场景需求。
- **多态/树结构**：如遇到树结构或递归模型，Pydantic 支持递归类型定义，可直接建模。

---

## 7. 推荐开发流程

1. **接口调研** → 2. Pydantic 建模 → 3. Service 层实现 → 4. 单元测试 → 5. 文档完善 → 6. 代码 Review → 7. 持续集成 -> 8. 此轮工作内容总结

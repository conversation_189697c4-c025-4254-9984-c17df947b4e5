"""
Equipment template service for MeOS Python SDK.

This module provides equipment template operations.
Tag: 设备模板信息
"""

import logging
from typing import Optional

from meos.services.common import CommonAttributeServiceMixin, CommonInstanceServiceMixin

from ..models.instances import (
    CommonBaseInfoListRequest,
    CommonBaseInfoListResponse,
    CommonDynamicInfoListRequest,
    CommonDynamicPropertyListResponse,
    CommonStaticInfoListRequest,
    CommonStaticPropertyListResponse,
    EquipmentInstanceResponse,
)

logger = logging.getLogger(__name__)


class EquipmentTemplateService(CommonAttributeServiceMixin, CommonInstanceServiceMixin):
    """Equipment template service for MeOS API - 设备模板信息."""

    EQUIPMENT_MODEL_INFO_ENDPOINT = "/p/dt/v1/public/model/eq/info"
    BASE_PROPERTIES_ENDPOINT = "/p/dt/v1/public/model/eq/page/base"
    DYNAMIC_PROPERTIES_ENDPOINT = "/p/dt/v1/public/model/eq/page/dynamic"
    STATIC_PROPERTIES_ENDPOINT = "/p/dt/v1/public/model/eq/page/static"

    # 请在此处插入 endpoint 类属性提取和方法内引用的修正代码，确保所有接口 method/url 只在类属性中定义一次，方法体全部引用类属性。

    def get_equipment_model_info(self, dataCode: Optional[str] = None) -> EquipmentInstanceResponse:
        """
        获取设备模型详细信息.
        Args:
            dataCode: 设备编码 (必填)
        Returns:
            完整的API响应, 包含 {code, msg, data} 结构
        Raises:
            MeOSAPIError: If the request fails
        """
        return self.get_instance_info(
            self.EQUIPMENT_MODEL_INFO_ENDPOINT,
            dataCode=dataCode,
            response_model=EquipmentInstanceResponse,
        )

    async def get_equipment_model_info_async(self, dataCode: Optional[str] = None) -> EquipmentInstanceResponse:
        """
        获取设备模型详细信息 (异步).
        Args:
            dataCode: 设备编码 (必填)
        Returns:
            完整的API响应, 包含 {code, msg, data} 结构
        Raises:
            MeOSAPIError: If the request fails
        """
        return await self.get_instance_info_async(
            self.EQUIPMENT_MODEL_INFO_ENDPOINT,
            dataCode=dataCode,
            response_model=EquipmentInstanceResponse,
        )

    def get_base_properties(self, req: CommonBaseInfoListRequest) -> CommonBaseInfoListResponse:
        """
        获取基础属性列表.
        Args:
            req: CommonBaseInfoListRequest
        Returns:
            完整的API响应, 包含 {code, msg, data} 结构
        Raises:
            MeOSAPIError: If the request fails
        """
        return self.get_common_base_properties(
            self.BASE_PROPERTIES_ENDPOINT,
            req,
        )

    async def get_base_properties_async(self, req: CommonBaseInfoListRequest) -> CommonBaseInfoListResponse:
        """
        获取基础属性列表 (异步).
        Args:
            req: CommonBaseInfoListRequest
        Returns:
            完整的API响应, 包含 {code, msg, data} 结构
        Raises:
            MeOSAPIError: If the request fails
        """
        return await self.get_common_base_properties_async(
            self.BASE_PROPERTIES_ENDPOINT,
            req,
        )

    def get_dynamic_properties(self, req: CommonDynamicInfoListRequest) -> CommonDynamicPropertyListResponse:
        """
        获取动态属性列表.
        Args:
            req: CommonDynamicInfoListRequest
        Returns:
            完整的API响应, 包含 {code, msg, data} 结构
        Raises:
            MeOSAPIError: If the request fails
        """
        return self.get_common_dynamic_properties(
            self.DYNAMIC_PROPERTIES_ENDPOINT,
            req,
        )

    async def get_dynamic_properties_async(
        self, req: CommonDynamicInfoListRequest
    ) -> CommonDynamicPropertyListResponse:
        """
        获取动态属性列表 (异步).
        Args:
            req: CommonDynamicInfoListRequest
        Returns:
            完整的API响应, 包含 {code, msg, data} 结构
        Raises:
            MeOSAPIError: If the request fails
        """
        return await self.get_common_dynamic_properties_async(
            self.DYNAMIC_PROPERTIES_ENDPOINT,
            req,
        )

    def get_static_properties(self, req: CommonStaticInfoListRequest) -> CommonStaticPropertyListResponse:
        """
        获取静态属性列表.
        Args:
            req: CommonStaticInfoListRequest
        Returns:
            完整的API响应, 包含 {code, msg, data} 结构
        Raises:
            MeOSAPIError: If the request fails
        """
        return self.get_common_static_properties(
            self.STATIC_PROPERTIES_ENDPOINT,
            req,
        )

    async def get_static_properties_async(self, req: CommonStaticInfoListRequest) -> CommonStaticPropertyListResponse:
        """
        获取静态属性列表 (异步).
        Args:
            req: CommonStaticInfoListRequest
        Returns:
            完整的API响应, 包含 {code, msg, data} 结构
        Raises:
            MeOSAPIError: If the request fails
        """
        return await self.get_common_static_properties_async(
            self.STATIC_PROPERTIES_ENDPOINT,
            req,
        )

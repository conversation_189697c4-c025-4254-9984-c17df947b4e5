---
type: "manual"
---

Based on all service SDK files in the current `services/` directory, create comprehensive unit test files with the following requirements:

**Test Structure:**
- Create separate test files for each service module following the naming pattern `test_{service_name}.py`
- Organize tests into test classes, with one class per service (e.g., `TestUserService`, `TestDeviceService`)
- Place all test files in a `tests/` directory with proper `__init__.py` files for package structure

**Testing Framework:**
- Use `pytest` as the primary testing framework with `unittest.mock` for mocking
- Include `pytest-asyncio` for testing async methods
- Use `pytest-httpx` for mocking HTTP requests made by httpx clients

**Test Coverage Requirements:**
- Test both synchronous and asynchronous methods for each service
- Mock all external HTTP requests using `httpx.MockTransport` or `pytest-httpx`
- Test successful responses (2xx status codes) and error responses (4xx, 5xx)
- Validate request parameters, headers, and URL construction
- Test Pydantic model validation for both request and response models
- Include edge cases and error handling scenarios

**Mock Strategy:**
- Mock the underlying `httpx.Client` and `httpx.AsyncClient` instances
- Create realistic mock responses that match the actual API response schemas
- Test different response status codes and their corresponding response models
- Mock authentication and authorization scenarios if applicable

**Test Organization:**
- Group related test methods within each test class
- Use descriptive test method names following the pattern `test_{method_name}_{scenario}`
- Include setup and teardown methods for common test fixtures
- Use pytest fixtures for reusable test data and mock configurations

**Additional Requirements:**
- Include docstrings for test classes and complex test methods
- Add type hints where appropriate
- Follow PEP 8 coding standards
- Include assertions for both successful operations and expected exceptions
- Test parameter validation and error messages from Pydantic models
- Ensure tests are independent and can run in any order

**Example Test Structure:**
```python
# tests/test_user_service.py
import pytest
from unittest.mock import Mock, AsyncMock
import httpx
from your_sdk.services.user_service import UserService
from your_sdk.models.user_models import GetUserRequest, GetUserResponse

class TestUserService:
    @pytest.fixture
    def mock_client(self):
        return Mock(spec=httpx.Client)

    @pytest.fixture
    def user_service(self, mock_client):
        return UserService(mock_client)

    def test_get_user_success(self, user_service, mock_client):
        # Test implementation here
        pass

    @pytest.mark.asyncio
    async def test_get_user_async_success(self, async_user_service, mock_async_client):
        # Async test implementation here
        pass
